#!/usr/bin/env python3
"""
Final verification of all database fixes
"""
import asyncio
import asyncpg

async def final_verification():
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    conn = await asyncpg.connect(url)
    
    print("🎯 FINAL DATABASE VERIFICATION")
    print("=" * 40)
    
    # Check all table counts
    tables = ['Element', 'Message', 'Feedback', 'Thread', 'User', 'Step', 'pitch_evaluations', 'student_sessions']
    
    for table in tables:
        try:
            count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table}";')
            status = "✅" if count > 0 else "⚠️"
            print(f"{status} {table}: {count} rows")
        except Exception as e:
            print(f"❌ {table}: Error - {e}")
    
    print()
    
    # Check recent messages
    recent_messages = await conn.fetch("""
        SELECT m.author, m.content, m."authorIsUser", m."createdAt"
        FROM "Message" m
        ORDER BY m."createdAt" DESC
        LIMIT 5;
    """)
    
    print("💬 RECENT MESSAGES:")
    for msg in recent_messages:
        author_type = "User" if msg['authorIsUser'] else "AI"
        content_preview = msg['content'][:60] + "..." if len(msg['content']) > 60 else msg['content']
        print(f"  {author_type}: {content_preview}")
    
    print()
    
    # Check student data
    student_data = await conn.fetch("""
        SELECT student_id, current_step_index, total_interactions, last_updated
        FROM student_sessions
        ORDER BY last_updated DESC
        LIMIT 3;
    """)
    
    print("👥 RECENT STUDENT SESSIONS:")
    for student in student_data:
        print(f"  Student {student['student_id']}: Step {student['current_step_index']}, {student['total_interactions']} interactions")
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(final_verification())
