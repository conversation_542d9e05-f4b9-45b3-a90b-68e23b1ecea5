import shutil
import os

def backup_file(source_path, backup_folder):
    """
    Backs up the specified source file to the backup folder.
    
    Args:
        source_path (str): Path of the file to back up.
        backup_folder (str): Folder where the backup will be stored.
    """
    if not os.path.exists(source_path):
        print(f"Source file '{source_path}' does not exist.")
        return
    
    if not os.path.exists(backup_folder):
        os.makedirs(backup_folder)
        print(f"Created backup folder '{backup_folder}'.")
    
    basename = os.path.basename(source_path)
    backup_path = os.path.join(backup_folder, basename)
    
    try:
        shutil.copy(source_path, backup_path)
        print(f"Backed up '{source_path}' to '{backup_path}'.")
    except Exception as e:
        print(f"Error during backup: {e}")

if __name__ == '__main__':
    # Set the source file and backup destination folder
    source_file = "solutions/langchain/my_agent_bot.py"
    backup_folder = "backup"
    
    backup_file(source_file, backup_folder)