#!/bin/bash
set -e

# Print environment for debugging
echo "Environment variables:"
env | grep -v "OPENAI_API_KEY\|DATABASE_URL"

# Run database setup
echo "Running database setup..."
python setup_chainlit_db_complete.py

# Start health check server in the background
echo "Starting health check server..."
python health_check.py &
HEALTH_PID=$!

# Give the health check server a moment to start
sleep 2

# Start Chainlit
echo "Starting Chainlit application..."
chainlit run solutions/langchain/my_agent_bot.py --host 0.0.0.0 --port $PORT

# If Chainlit exits, kill the health check server
kill $HEALTH_PID
