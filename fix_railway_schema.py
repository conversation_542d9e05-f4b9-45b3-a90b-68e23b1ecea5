#!/usr/bin/env python3
"""
Railway Database Schema Fix Script
This script fixes the schema mismatch issues in your Railway PostgreSQL database.
Run this script on Railway to fix the database schema.
"""
import os
import asyncio
import asyncpg
import logging
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def fix_database_schema():
    """Fix the database schema to match Chainlit's expectations."""

    # Get database URL - try Railway internal URL first, then fall back to environment variable
    database_url = "postgresql://postgres:<EMAIL>:5432/railway"

    # For local execution, use the external Railway URL
    external_database_url = "postgresql://postgres:<EMAIL>:20482/railway"

    try:
        # Try connecting with Railway internal URL first
        conn = await asyncpg.connect(database_url)
        logging.info("✅ Connected to database using Railway internal URL")
    except Exception as db_error:
        # If that fails, try the external Railway URL
        logging.warning(f"Failed to connect with Railway internal URL: {db_error}")
        try:
            conn = await asyncpg.connect(external_database_url)
            logging.info("✅ Connected to database using Railway external URL")
        except Exception as external_error:
            # If that fails, try the environment variable
            logging.warning(f"Failed to connect with Railway external URL: {external_error}")
            database_url = os.getenv("DATABASE_URL")
            if not database_url:
                logging.error("❌ DATABASE_URL environment variable not set")
                return False
            conn = await asyncpg.connect(database_url)
            logging.info("✅ Connected to database using DATABASE_URL environment variable")

    try:
        logging.info("🔧 Starting database schema fix...")

        # 1. Check existing tables
        logging.info("📋 Checking existing tables...")
        tables_query = """
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """
        tables = await conn.fetch(tables_query)
        existing_tables = [table['table_name'] for table in tables]
        logging.info(f"Found tables: {existing_tables}")

        # 2. Fix Feedback table schema
        if 'feedback' in existing_tables or 'Feedback' in existing_tables:
            logging.info("🔧 Fixing Feedback table schema...")

            # Determine the actual table name (case-sensitive)
            feedback_table_name = 'Feedback' if 'Feedback' in existing_tables else 'feedback'

            # Check current columns in Feedback table
            feedback_columns = await conn.fetch("""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = $1
                ORDER BY ordinal_position;
            """, feedback_table_name)

            column_names = [col['column_name'] for col in feedback_columns]
            logging.info(f"Current Feedback table columns: {column_names}")

            # Add missing 'name' column if it doesn't exist
            if 'name' not in column_names:
                await conn.execute(f'ALTER TABLE "{feedback_table_name}" ADD COLUMN name VARCHAR(255);')
                logging.info(f"✅ Added 'name' column to {feedback_table_name} table")
            else:
                logging.info(f"✅ 'name' column already exists in {feedback_table_name} table")

            # Add missing 'forId' column if it doesn't exist (Chainlit requirement)
            if 'forId' not in column_names and 'forid' not in column_names:
                await conn.execute(f'ALTER TABLE "{feedback_table_name}" ADD COLUMN "forId" UUID;')
                logging.info(f"✅ Added 'forId' column to {feedback_table_name} table")
            else:
                logging.info(f"✅ 'forId' column already exists in {feedback_table_name} table")
        else:
            logging.info("🔧 Creating Feedback table...")
            await conn.execute("""
                CREATE TABLE feedback (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    "forId" UUID,
                    name VARCHAR(255),
                    value INTEGER,
                    comment TEXT,
                    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            logging.info("✅ Created Feedback table with correct schema")

        # 3. Ensure other Chainlit tables have correct schema
        logging.info("🔧 Ensuring other Chainlit tables have correct schema...")

        # Create/update Element table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS element (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                "threadId" UUID,
                type VARCHAR(255),
                url TEXT,
                name VARCHAR(255),
                display VARCHAR(255),
                size BIGINT,
                language VARCHAR(255),
                "forId" UUID,
                mime VARCHAR(255),
                "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        logging.info("✅ Element table created/updated")

        # Create/update Message table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS message (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                "threadId" UUID,
                content TEXT,
                "authorId" UUID,
                "parentId" UUID,
                indent INTEGER DEFAULT 0,
                "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        logging.info("✅ Message table created/updated")

        # Create/update Thread table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS thread (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255),
                "userId" UUID,
                "userIdentifier" VARCHAR(255),
                tags TEXT[],
                metadata JSONB,
                "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        logging.info("✅ Thread table created/updated")

        # Create/update User table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "user" (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                identifier VARCHAR(255) UNIQUE,
                metadata JSONB,
                "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        logging.info("✅ User table created/updated")

        # Create/update Step table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS step (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255),
                type VARCHAR(255),
                "threadId" UUID,
                "parentId" UUID,
                "disableFeedback" BOOLEAN DEFAULT FALSE,
                streaming BOOLEAN DEFAULT FALSE,
                "waitForAnswer" BOOLEAN DEFAULT FALSE,
                "isError" BOOLEAN DEFAULT FALSE,
                metadata JSONB,
                tags TEXT[],
                input TEXT,
                output TEXT,
                "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                start TIMESTAMP WITH TIME ZONE,
                "end" TIMESTAMP WITH TIME ZONE
            );
        """)
        logging.info("✅ Step table created/updated")

        # 4. Create custom tables for your application
        logging.info("🔧 Creating custom application tables...")

        # Create student_sessions table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS student_sessions (
                id SERIAL PRIMARY KEY,
                student_id VARCHAR(8) UNIQUE NOT NULL,
                current_step_index INTEGER DEFAULT 0,
                total_interactions INTEGER DEFAULT 0,
                completed_steps INTEGER DEFAULT 0,
                last_message_content TEXT,
                last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        logging.info("✅ student_sessions table created/updated")

        # Create pitch_evaluations table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS pitch_evaluations (
                id SERIAL PRIMARY KEY,
                student_id VARCHAR(8) NOT NULL,
                step_name VARCHAR(255) NOT NULL,
                score INTEGER,
                feedback TEXT,
                evaluation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        logging.info("✅ pitch_evaluations table created/updated")

        # 5. Add indexes for better performance
        logging.info("🔧 Adding indexes for better performance...")

        try:
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_thread_user_identifier ON thread("userIdentifier");')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_message_thread_id ON message("threadId");')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_element_thread_id ON element("threadId");')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_step_thread_id ON step("threadId");')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_feedback_for_id ON feedback("forId");')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_student_sessions_student_id ON student_sessions(student_id);')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_pitch_evaluations_student_id ON pitch_evaluations(student_id);')
            logging.info("✅ Indexes created successfully")
        except Exception as e:
            logging.warning(f"Some indexes may already exist: {e}")

        # 6. Check final table counts
        logging.info("📊 Final table row counts:")
        # Use the actual table names from the database
        table_mapping = {
            'Element': 'Element',
            'Message': 'Message',
            'Feedback': 'Feedback',
            'Thread': 'Thread',
            'User': 'User',
            'Step': 'Step',
            'student_sessions': 'student_sessions',
            'pitch_evaluations': 'pitch_evaluations'
        }

        for display_name, actual_table_name in table_mapping.items():
            try:
                if actual_table_name in existing_tables:
                    count_query = f'SELECT COUNT(*) as count FROM "{actual_table_name}";'
                    result = await conn.fetchrow(count_query)
                    logging.info(f"  - {display_name}: {result['count']} rows")
                else:
                    logging.info(f"  - {display_name}: Table not found")
            except Exception as e:
                logging.warning(f"  - {display_name}: Error counting rows - {e}")

        await conn.close()
        logging.info("✅ Database schema fix completed successfully!")
        return True

    except Exception as e:
        logging.error(f"❌ Error fixing database schema: {e}")
        await conn.close()
        return False

async def test_feedback_insertion():
    """Test if feedback can be inserted after schema fix."""

    database_url = "postgresql://postgres:<EMAIL>:5432/railway"
    external_database_url = "postgresql://postgres:<EMAIL>:20482/railway"

    try:
        conn = await asyncpg.connect(database_url)
    except Exception:
        # Try external URL if internal fails
        conn = await asyncpg.connect(external_database_url)

        # Test inserting a feedback record (use correct table name)
        test_feedback_id = await conn.fetchval("""
            INSERT INTO "Feedback" (name, value, comment, "forId")
            VALUES ($1, $2, $3, $4)
            RETURNING id;
        """, "test_feedback", 5, "This is a test feedback", None)

        logging.info(f"✅ Test feedback inserted successfully with ID: {test_feedback_id}")

        # Clean up test record
        await conn.execute('DELETE FROM "Feedback" WHERE id = $1;', test_feedback_id)
        logging.info("✅ Test feedback cleaned up")

        await conn.close()
        return True

    except Exception as e:
        logging.error(f"❌ Error testing feedback insertion: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Railway Database Schema Fix...")

    # Run schema fix
    success = asyncio.run(fix_database_schema())

    if success:
        print("\n🧪 Testing feedback insertion...")
        test_success = asyncio.run(test_feedback_insertion())

        if test_success:
            print("\n🎉 Database schema fix completed successfully!")
            print("✅ Your Railway database is now ready for Chainlit data persistence.")
        else:
            print("\n⚠️ Schema fix completed but feedback test failed.")
    else:
        print("\n❌ Database schema fix failed. Check the logs above for details.")
