#!/usr/bin/env python3
"""
Fix duplicate tables and ensure Element/Message tables work properly
"""
import asyncio
import asyncpg
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def fix_duplicate_tables():
    """Fix duplicate tables and schema issues."""
    
    # External Railway URL for local execution
    database_url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(database_url)
        logging.info("✅ Connected to Railway database")
        
        logging.info("🔧 Starting duplicate table cleanup and schema fix...")
        
        # 1. Check current table status
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        existing_tables = [table['table_name'] for table in tables]
        logging.info(f"Found tables: {existing_tables}")
        
        # 2. Remove duplicate lowercase tables (they're empty anyway)
        duplicate_tables = ['element', 'message', 'step', 'thread', 'user']
        
        for table_name in duplicate_tables:
            if table_name in existing_tables:
                logging.info(f"🗑️ Removing duplicate table: {table_name}")
                try:
                    await conn.execute(f'DROP TABLE IF EXISTS "{table_name}" CASCADE;')
                    logging.info(f"✅ Removed duplicate table: {table_name}")
                except Exception as e:
                    logging.warning(f"⚠️ Could not remove {table_name}: {e}")
        
        # 3. Ensure the main tables have correct Chainlit schema
        logging.info("🔧 Updating main table schemas for Chainlit compatibility...")
        
        # Fix Element table schema
        logging.info("🔧 Fixing Element table schema...")
        try:
            # Check current Element columns
            element_columns = await conn.fetch("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'Element' AND table_schema = 'public';
            """)
            current_columns = [col['column_name'] for col in element_columns]
            logging.info(f"Current Element columns: {current_columns}")
            
            # Add missing columns that Chainlit expects
            required_columns = {
                'id': 'UUID PRIMARY KEY DEFAULT gen_random_uuid()',
                'threadId': 'UUID',
                'type': 'VARCHAR(255)',
                'url': 'TEXT',
                'name': 'VARCHAR(255)',
                'display': 'VARCHAR(255)',
                'size': 'BIGINT',
                'language': 'VARCHAR(255)',
                'forId': 'UUID',
                'mime': 'VARCHAR(255)',
                'createdAt': 'TIMESTAMP WITH TIME ZONE DEFAULT NOW()'
            }
            
            for col_name, col_type in required_columns.items():
                if col_name not in current_columns:
                    try:
                        if col_name == 'id':
                            # Skip id if it already exists
                            continue
                        await conn.execute(f'ALTER TABLE "Element" ADD COLUMN "{col_name}" {col_type};')
                        logging.info(f"✅ Added {col_name} column to Element table")
                    except Exception as e:
                        logging.warning(f"⚠️ Could not add {col_name} to Element: {e}")
            
        except Exception as e:
            logging.error(f"❌ Error fixing Element table: {e}")
        
        # Fix Message table schema
        logging.info("🔧 Fixing Message table schema...")
        try:
            # Check current Message columns
            message_columns = await conn.fetch("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'Message' AND table_schema = 'public';
            """)
            current_columns = [col['column_name'] for col in message_columns]
            logging.info(f"Current Message columns: {current_columns}")
            
            # Add missing columns that Chainlit expects
            required_columns = {
                'id': 'UUID PRIMARY KEY DEFAULT gen_random_uuid()',
                'threadId': 'UUID',
                'content': 'TEXT',
                'authorId': 'UUID',
                'parentId': 'UUID',
                'indent': 'INTEGER DEFAULT 0',
                'createdAt': 'TIMESTAMP WITH TIME ZONE DEFAULT NOW()'
            }
            
            for col_name, col_type in required_columns.items():
                if col_name not in current_columns:
                    try:
                        if col_name == 'id':
                            # Skip id if it already exists
                            continue
                        await conn.execute(f'ALTER TABLE "Message" ADD COLUMN "{col_name}" {col_type};')
                        logging.info(f"✅ Added {col_name} column to Message table")
                    except Exception as e:
                        logging.warning(f"⚠️ Could not add {col_name} to Message: {e}")
            
        except Exception as e:
            logging.error(f"❌ Error fixing Message table: {e}")
        
        # 4. Create indexes for better performance
        logging.info("🔧 Adding performance indexes...")
        try:
            indexes = [
                'CREATE INDEX IF NOT EXISTS idx_element_thread_id ON "Element"("threadId");',
                'CREATE INDEX IF NOT EXISTS idx_element_for_id ON "Element"("forId");',
                'CREATE INDEX IF NOT EXISTS idx_message_thread_id ON "Message"("threadId");',
                'CREATE INDEX IF NOT EXISTS idx_message_author_id ON "Message"("authorId");',
                'CREATE INDEX IF NOT EXISTS idx_message_parent_id ON "Message"("parentId");'
            ]
            
            for index_sql in indexes:
                try:
                    await conn.execute(index_sql)
                except Exception as e:
                    logging.warning(f"Index may already exist: {e}")
            
            logging.info("✅ Performance indexes added")
            
        except Exception as e:
            logging.warning(f"⚠️ Some indexes may already exist: {e}")
        
        # 5. Test Element insertion
        logging.info("🧪 Testing Element table insertion...")
        try:
            test_element_id = await conn.fetchval("""
                INSERT INTO "Element" (type, name, url, mime, "threadId")
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id;
            """, "file", "test_file.txt", "/test/url", "text/plain", None)
            
            logging.info(f"✅ Test element inserted with ID: {test_element_id}")
            
            # Clean up test record
            await conn.execute('DELETE FROM "Element" WHERE id = $1;', test_element_id)
            logging.info("✅ Test element cleaned up")
            
        except Exception as e:
            logging.error(f"❌ Error testing Element insertion: {e}")
        
        # 6. Test Message insertion
        logging.info("🧪 Testing Message table insertion...")
        try:
            test_message_id = await conn.fetchval("""
                INSERT INTO "Message" (content, "threadId", "authorId")
                VALUES ($1, $2, $3)
                RETURNING id;
            """, "Test message content", None, None)
            
            logging.info(f"✅ Test message inserted with ID: {test_message_id}")
            
            # Clean up test record
            await conn.execute('DELETE FROM "Message" WHERE id = $1;', test_message_id)
            logging.info("✅ Test message cleaned up")
            
        except Exception as e:
            logging.error(f"❌ Error testing Message insertion: {e}")
        
        # 7. Final table status
        logging.info("📊 Final table status:")
        final_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        
        for table in final_tables:
            table_name = table['table_name']
            try:
                count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table_name}";')
                logging.info(f"  - {table_name}: {count} rows")
            except Exception as e:
                logging.warning(f"  - {table_name}: Error counting - {e}")
        
        await conn.close()
        logging.info("✅ Database cleanup and schema fix completed successfully!")
        return True
        
    except Exception as e:
        logging.error(f"❌ Error fixing database: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Railway Database Duplicate Table Fix...")
    
    success = asyncio.run(fix_duplicate_tables())
    
    if success:
        print("\n🎉 Database fix completed successfully!")
        print("✅ Duplicate tables removed")
        print("✅ Element and Message tables fixed")
        print("✅ Your Railway database is now optimized for Chainlit")
    else:
        print("\n❌ Database fix failed. Check the logs above for details.")
