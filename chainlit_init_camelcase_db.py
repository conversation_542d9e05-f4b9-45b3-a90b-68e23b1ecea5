import os
import asyncpg
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def init_chainlit_tables():
    """Initialize the required tables for Chainlit data persistence with camelCase column names."""
    load_dotenv()
    
    try:
        # Connect to the database
        conn = await asyncpg.connect(os.getenv("DATABASE_URL"))
        
        # Create Thread table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Thread" (
                id UUID PRIMARY KEY,
                createdAt TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                name TEXT,
                tags TEXT[],
                metadata JSONB DEFAULT '{}'::jsonb,
                userId TEXT,
                userIdentifier TEXT,
                feedbackStatus TEXT,
                humanFeedback INTEGER,
                humanFeedbackComment TEXT
            )
        """)
        
        # Create Step table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Step" (
                id UUID PRIMARY KEY,
                threadId UUID REFERENCES "Thread"(id),
                parentId UUID,
                name TEXT,
                type TEXT,
                input TEXT,
                output TEXT,
                createdAt TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                startTime TIMESTAMP WITHOUT TIME ZONE,
                endTime TIMESTAMP WITHOUT TIME ZONE,
                metadata JSONB DEFAULT '{}'::jsonb,
                error TEXT,
                feedback INTEGER,
                feedbackComment TEXT
            )
        """)
        
        # Create Element table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Element" (
                id UUID PRIMARY KEY,
                threadId UUID REFERENCES "Thread"(id),
                stepId UUID REFERENCES "Step"(id),
                type TEXT,
                name TEXT,
                url TEXT,
                display TEXT,
                language TEXT,
                size INTEGER,
                createdAt TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                forId TEXT,
                mime TEXT,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        """)
        
        # Create Message table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Message" (
                id UUID PRIMARY KEY,
                threadId UUID REFERENCES "Thread"(id),
                createdAt TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                content TEXT,
                author TEXT,
                authorIsUser BOOLEAN,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        """)
        
        logging.info("✅ Chainlit database tables created successfully with camelCase column names")
        
        # Close the connection
        await conn.close()
        
    except Exception as e:
        logging.error(f"❌ Chainlit database initialization failed: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(init_chainlit_tables())
