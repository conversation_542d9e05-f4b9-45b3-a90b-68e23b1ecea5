# Mentor Agent Prompt Evolution Report
## AI Elevator Pitch Tutor - Master's Thesis Appendix

**Author**: <PERSON>  
**Date**: May 28, 2025  
**Experiment Date**: June 2, 2025  
**Document Purpose**: Thesis Appendix - Prompt Engineering Evolution

---

## Executive Summary

This report documents the systematic evolution of the Mentor Agent's prompt template in the AI Elevator Pitch Tutor system, tracing its development from a simple conversational agent to a sophisticated, context-aware educational assistant. The evolution reflects iterative improvements based on user testing, pedagogical considerations, and experimental requirements.

---

## 1. Initial Version (Early Development)
**File**: `backup/my_agent_bot.py`  
**Characteristics**: Basic conversational framework

```python
template="""
[SYSTEM MESSAGE]
You are the Mentor Agent, guiding students through the process of crafting an elevator pitch. Your role is to:
- NEVER provide a full elevator pitch immediately.
- ALWAYS ask thought-provoking questions that encourage critical thinking.
- Challenge students to justify their ideas before moving forward.
- Offer hints and feedback rather than direct answers.
- If the user asks "Who are you?", respond "I am the Mentor Agent, here to guide you through building your best elevator pitch."

Conversation History:
{context}

Student Message:
{question}

Mentor's Guided Response:
"""
```

**Key Features**:
- Simple input variables: `context`, `question`
- Emphasis on challenging students
- No structured step progression
- Focus on critical thinking and justification

**Limitations**:
- No step-by-step guidance
- Overly challenging approach
- No progress tracking
- Limited pedagogical structure

---

## 2. Step-Aware Version (Mid Development)
**File**: `solutions/langchain/Chainlit new my_agent_bot.py`  
**Characteristics**: Introduction of structured step progression

```python
template="""
[SYSTEM MESSAGE]
You are the Mentor Agent, guiding students through creating an elevator pitch.
🚀 **Current Step:** {current_step}
- NEVER skip a step in the process.
- Ask questions that ensure students fully understand each step before moving forward.
- Challenge their responses and encourage reflection.
- If they try to skip ahead, redirect them to the current step.

**Current Progress:**
{context}

**Student Question:**
{question}

**Mentor's Guided Response:**
"""
```

**Key Features**:
- Added `current_step` input variable
- Structured step progression
- Step enforcement mechanisms
- Visual step indicators (🚀 emoji)

**Improvements**:
- Clear step-by-step guidance
- Prevention of step skipping
- Better progress visualization

**Remaining Issues**:
- Still overly challenging
- No step completion logic
- Limited context about overall progress

---

## 3. Enhanced Context Version (Late Development)
**File**: `solutions/langchain/Chainlit assistant @310325 my_agent_bot.py`  
**Characteristics**: Compressed but comprehensive guidance

```python
template="""[SYSTEM MESSAGE]
You are the Mentor Agent, guiding students through creating an elevator pitch. 
🚀 **Current Step:** {current_step} - NEVER skip a step in the process.
Ask questions that ensure students fully understand each step before moving forward. Challenge their responses and encourage reflection. If they try to skip ahead, redirect them to the current step.
**Current Progress:** {context}
**Student Question:** {question}
**Mentor's Guided Response:**"""
```

**Key Features**:
- Condensed format for efficiency
- Maintained step enforcement
- Streamlined structure

**Design Philosophy**:
- Balance between guidance and challenge
- Emphasis on understanding before progression

---

## 4. Express Mode Integration (Advanced Development)
**File**: `solutions/langchain/my_agent_bot_with_express_mode.py`  
**Characteristics**: Dual-mode functionality

```python
template="""
[SYSTEM MESSAGE]
You are the Mentor Agent, guiding students through creating an elevator pitch.

🚀 **Current Step ({current_step_index}/{total_steps}):** {current_step}

📋 **All Steps:**
{pitch_steps}

⏱️ **Mode:** {{'Express Mode' if express_mode else 'Detailed Mode'}}

{{% if express_mode %}}
You are in EXPRESS MODE:
- Be concise and direct in your guidance
- Limit yourself to 1-2 essential questions per step
- Provide examples and templates to speed up the process
- When the student provides a reasonable answer, quickly move to the next step
- The entire process should take no more than 15-20 minutes
- Use phrases like "This looks good! Let's move to the next step" when appropriate
- Add "[STEP_COMPLETED]" at the end of your response when ready to move on
{{% else %}}
You are in DETAILED MODE:
- Ask questions that ensure students fully understand each step
- Challenge their responses and encourage reflection
- If they try to skip ahead, redirect them to the current step
{{% endif %}}
```

**Key Features**:
- Dual-mode operation (Express/Detailed)
- Step completion markers (`[STEP_COMPLETED]`)
- Progress indicators (`current_step_index/total_steps`)
- Complete step overview (`pitch_steps`)
- Time-based guidance (15-20 minutes for Express)

**Major Innovations**:
- Adaptive guidance based on user preference
- Automated step progression logic
- Comprehensive context awareness

---

## 5. Current Version - Balanced Guidance (Final Implementation)
**File**: `solutions/langchain/my_agent_bot.py`  
**Characteristics**: Pedagogically optimized for undergraduate students

```python
template="""
[SYSTEM MESSAGE]
You are the Mentor Agent, guiding students through creating an elevator pitch.

🚀 **Current Step ({current_step_index}/{total_steps}):** {current_step}

📋 **All Steps:**
{pitch_steps}

🔍 **BALANCED GUIDANCE MODE** 🔍

You MUST follow these guidelines:
- Ask 1-2 focused questions for each step that are appropriate for undergraduate students
- Be encouraging and supportive rather than challenging
- Keep your responses concise and practical (no more than 3-4 paragraphs)
- If the student types "/next" or says they want to move to the next step, add "[STEP_COMPLETED]" to your response
- Add "[STEP_COMPLETED]" after 2-3 meaningful exchanges if the student has provided reasonable answers
- Avoid overwhelming the student with too many questions at once
- Focus on practical advice rather than theoretical concepts
- If the student seems confused, simplify your guidance

**Current Progress:**
{context}

**Student Question:**
{question}

**Mentor's Guided Response:**
"""
```

**Key Features**:
- **Balanced Guidance Mode**: Supportive rather than challenging
- **Undergraduate-Appropriate**: Tailored for student skill level
- **Interaction Tracking**: 2-3 meaningful exchanges before progression
- **Practical Focus**: Emphasis on actionable advice
- **Confusion Detection**: Adaptive simplification
- **Manual Override**: `/next` command support

---

## Evolution Analysis

### 1. Pedagogical Philosophy Shift
- **Early**: Challenging, critical thinking focused
- **Current**: Supportive, encouraging, practical

### 2. Structural Complexity Growth
- **Early**: 2 input variables (`context`, `question`)
- **Current**: 6 input variables (`context`, `question`, `current_step`, `pitch_steps`, `current_step_index`, `total_steps`)

### 3. User Experience Improvements
- **Step Visualization**: Progress indicators and step lists
- **Mode Flexibility**: Express vs. Detailed (later simplified to Balanced)
- **Interaction Control**: Manual progression commands
- **Adaptive Responses**: Confusion detection and simplification

### 4. Technical Sophistication
- **Step Completion Logic**: Automated progression markers
- **Context Awareness**: Full step overview and progress tracking
- **Interaction Counting**: Minimum engagement requirements
- **Content Validation**: Meaningful response detection

---

## Key Design Decisions and Rationale

### 1. From Challenging to Supportive (Critical Change)
**Rationale**: User testing revealed that overly challenging prompts created anxiety and reduced learning effectiveness among undergraduate students.

**Implementation**: Changed from "Challenge students to justify their ideas" to "Be encouraging and supportive rather than challenging"

### 2. Introduction of Step Completion Markers
**Rationale**: Needed automated way to detect when students were ready to progress without manual intervention.

**Implementation**: `[STEP_COMPLETED]` marker system with intelligent parsing

### 3. Interaction Counting Mechanism
**Rationale**: Prevent premature advancement while ensuring meaningful engagement.

**Implementation**: "2-3 meaningful exchanges" requirement before step progression

### 4. Simplification from Dual-Mode to Balanced Mode
**Rationale**: Express/Detailed modes created complexity without significant user benefit.

**Implementation**: Single "Balanced Guidance Mode" optimized for 10-20 minute sessions

---

## Experimental Considerations

### Target Audience Optimization
- **Undergraduate Students**: Age-appropriate language and expectations
- **10-20 Minute Sessions**: Balanced between thoroughness and efficiency
- **5-10 Students per Group**: Scalable interaction patterns

### Data Collection Requirements
- **Interaction Tracking**: Each exchange logged for analysis
- **Step Progression**: Timing and advancement patterns recorded
- **Student Engagement**: Response quality and length metrics

### Thesis Experiment Alignment (June 2, 2025)
- **Consistent Experience**: Standardized prompt ensures comparable data
- **Measurable Outcomes**: Clear step completion criteria
- **Scalable Design**: Handles multiple concurrent users

---

## Technical Implementation Details

### Input Variables Evolution
1. **Version 1**: `context`, `question`
2. **Version 2**: `context`, `question`, `current_step`
3. **Version 3**: `context`, `question`, `current_step`, `express_mode`, `pitch_steps`, `current_step_index`, `total_steps`
4. **Version 4**: `context`, `question`, `current_step`, `pitch_steps`, `current_step_index`, `total_steps`

### Step Completion Logic
```python
if "[STEP_COMPLETED]" in ai_text and selected_agent == "mentor":
    # Validation logic for meaningful content
    # Interaction count verification
    # Step advancement or completion
```

### Interaction Tracking
```python
step_interactions = cl.user_session.get("step_interactions", {})
current_step_key = f"step_{current_step_index}"
interaction_count = step_interactions.get(current_step_key, 0)
```

---

## Conclusion

The Mentor Agent prompt evolution demonstrates a systematic approach to prompt engineering, moving from a generic conversational agent to a sophisticated educational tool. The final version balances pedagogical effectiveness with technical functionality, optimized for the specific requirements of the June 2, 2025 experiment.

The evolution reflects key learnings about:
- **Student-Centered Design**: Supportive rather than challenging approaches
- **Structured Learning**: Step-by-step progression with clear milestones
- **Adaptive Interaction**: Flexible response to student needs and confusion
- **Experimental Rigor**: Consistent, measurable interaction patterns

This prompt engineering process serves as a model for developing AI educational assistants that balance automation with pedagogical effectiveness.

---

**Document Version**: 1.0  
**Last Updated**: May 28, 2025  
**Next Review**: Post-experiment analysis (June 2025)
