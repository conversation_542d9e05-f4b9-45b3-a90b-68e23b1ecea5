// Simple Express server to proxy requests to Chainlit
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const app = express();
const port = process.env.PORT || 3000;

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).send('OK');
});

// Proxy all other requests to Chainlit
app.use('/', createProxyMiddleware({
  target: 'http://localhost:8000',
  changeOrigin: true,
}));

app.listen(port, () => {
  console.log(`Server listening on port ${port}`);
});
