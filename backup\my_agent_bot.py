import chainlit as cl
from langchain_core.callbacks import BaseCallbackHandler
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain.callbacks.manager import CallbackManager
import os
import logging
from langchain_core.messages import AIMessage
from typing import Any, Dict
from chainlit import user_session


# --- Setup Logging ---
logging.basicConfig(level=logging.INFO)

# --- Define LLM Model ---
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise ValueError("❌ ERROR: OpenAI API Key is missing! Set OPENAI_API_KEY environment variable.")
else:
    logging.info(f"🔑 API Key Loaded Successfully: *******{api_key[-5:]}")  # Only print last 5 characters for security


llm = ChatOpenAI(
    model="gpt-4o",  # Change to "gpt-4o" for better performance
    temperature=0.7,
    openai_api_key=api_key  
)

class StreamHandler(BaseCallbackHandler):
    def __init__(self, message_id: str):
        self.message_id = message_id
        self.content = ""

    async def on_llm_new_token(self, token: str, **kwargs):
        self.content += token
        await cl.Message(id=self.message_id, content=self.content).update()

    async def on_llm_end(self, response, **kwargs):
        pass

    async def on_llm_error(self, error, **kwargs):
        await cl.Message(id=self.message_id, content=f"Error: {error}").update()

# --- Define AI Agents ---
PROMPTS = {
    "mentor": PromptTemplate(
        input_variables=["context", "question"],
        template="""
        [SYSTEM MESSAGE]
        You are the Mentor Agent, guiding students through the process of crafting an elevator pitch. Your role is to:
        - NEVER provide a full elevator pitch immediately.
        - ALWAYS ask thought-provoking questions that encourage critical thinking.
        - Challenge students to justify their ideas before moving forward.
        - Offer hints and feedback rather than direct answers.
        - If the user asks "Who are you?", respond "I am the Mentor Agent, here to guide you through building your best elevator pitch."

        Conversation History:
        {context}

        Student Message:
        {question}

        Mentor's Guided Response:
        """
    ),
    "peer": PromptTemplate(
        input_variables=["context", "question"],
        template="""
        [SYSTEM MESSAGE]
        You are the Peer Agent, a fellow student helping another student brainstorm an elevator pitch.
        - Provide feedback and ask guiding questions.
        - Challenge unclear statements and encourage deeper thinking.
        - Offer alternative perspectives on the student’s ideas.
        - If the user asks "Who are you?", respond "I am the Peer Agent, your study partner for refining your elevator pitch."
        - DO NOT simply agree or approve—ask, "Have you thought about X?" or "How would you explain this to someone unfamiliar with your field?"

        Conversation History:
        {context}

        Student Message:
        {question}

        Peer’s Thoughtful Response:
        """
    ),
    "progress": PromptTemplate(
        input_variables=["context", "question"],
        template="""
        [SYSTEM MESSAGE]
        You are the Progress Agent, tracking the student’s progress through their elevator pitch creation.
        - NEVER assume a student is ready to move forward—ask them to reflect first.
        - If the student skips a step, gently redirect them to review it before proceeding.
        - Provide **specific** feedback on what has been completed and what needs improvement.
        - If the user asks "Who are you?", respond "I am the Progress Agent, ensuring you stay on track to create the best elevator pitch possible."

        Student's Current Progress:
        {student_progress}

        Conversation History:
        {context}

        Student Message:
        {question}

        Progress Tracking Response:
        """
    ),
}

# --- Memory Management ---
memory = {agent: ChatMessageHistory() for agent in PROMPTS.keys()}

# --- Create LLM Chains for Agents ---
agents = {agent: PROMPTS[agent] | llm for agent in PROMPTS.keys()}

# --- Chainlit Chat Handling ---
@cl.on_chat_start
async def start_chat():
    cl.user_session.set("agent", "mentor")  
    cl.user_session.set("student_progress", "No progress recorded yet.")
    active_agent = cl.user_session.get("agent").capitalize()
    welcome_message = f"""
    🎓 **Welcome to the AI Elevator Pitch Tutor!** 🏆

    🤖 This tutor is powered by **three AI agents**:
    - 🧑‍🏫 **Mentor Agent** → Guides you step-by-step, asking thought-provoking questions.
    - 👥 **Peer Agent** → Challenges your ideas like a study partner.
    - 📈 **Progress Agent** → Tracks your pitch progress and ensures you're on the right path.

    🔄 **Switch Agents Anytime!** Use:
    - `/mentor` → Switch to Mentor Agent.
    - `/peer` → Switch to Peer Agent.
    - `/progress` → Switch to Progress Agent.

    ✨ **Tip:** Don’t just ask for answers—engage with the agents and refine your ideas!

    🔹 **You are now talking to the {active_agent} Agent.**

    🚀 **Let's get started!** What do you need help with?
    """
    await cl.Message(welcome_message).send()

@cl.on_message
async def handle_message(message: cl.Message):
    selected_agent = cl.user_session.get("agent")

    if message.content.lower() in ["/mentor", "/peer", "/progress"]:
        cl.user_session.set("agent", message.content[1:])
        await cl.Message(f"🔹 Switched to **{message.content[1:].capitalize()} Agent**").send()
        return
      

    memory[selected_agent].add_user_message(message.content)
    logging.info(f"📝 User Input to {selected_agent}: {message.content}")
    
    try:
        response = await agents[selected_agent].ainvoke(
            {"question": message.content, "context": memory[selected_agent].messages},
            config={"callbacks": [StreamHandler(message.id), cl.AsyncLangchainCallbackHandler()]}
        )

        # Extract and log AI response
        if isinstance(response, AIMessage):
            ai_text = response.content
        elif isinstance(response, dict) and "content" in response:
            ai_text = response["content"]

        else:
            ai_text = response.text

        logging.info(f"🔹 Processed AI Response: {ai_text}")    
        
        # Ensure a valid response is returned
        if not ai_text or ai_text.strip() == "":
            ai_text = "I'm sorry, I couldn't generate a response. Please try again."
        
        memory[selected_agent].add_ai_message(ai_text)
        await cl.Message(content=ai_text).send()
    
    except Exception as e:
        logging.error(f"❌ Error in OpenAI API Call: {e}")
        await cl.Message(content=f"An error occurred: {e}").send()

@cl.on_stop
async def stop_execution():
    cl.user_session.clear()  
    for mem in memory.values():
        mem.clear()
    await cl.Message("Session stopped by user.").send()
