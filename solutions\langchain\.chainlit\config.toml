[project]
# Whether to enable telemetry (default: true). No personal data is collected.
enable_telemetry = false

# List of environment variables to be provided by each user to use the app.
user_env = []

# Duration (in seconds) during which the session is saved when the connection is lost
session_timeout = 7200  # 2 hours

# Duration (in seconds) of the user session expiry
user_session_timeout = 7200  # 2 hours for the experiment

# Enable third parties caching (e.g LangChain cache)
cache = false

# Authorized origins
allow_origins = ["*"]

[features]
# Process and display HTML in messages. This can be a security risk (see https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript)
unsafe_allow_html = false

# Process and display mathematical expressions. This can clash with "$" characters in messages.
latex = false

# Automatically tag threads with the current chat profile (if a chat profile is used)
auto_tag_thread = true

# Allow users to edit their own messages
edit_message = true

# Enable message feedback
[features.message_feedback]
    enabled = true
    # Enable thumbs up/down buttons
    thumbs = true
    # Enable comment submission with thumbs
    comment = true

# Authorize users to spontaneously upload files with messages
[features.spontaneous_file_upload]
    enabled = true
    # Define accepted file types using MIME types
    # Examples:
    # 1. For specific file types:
    #    accept = ["image/jpeg", "image/png", "application/pdf"]
    # 2. For all files of certain type:
    #    accept = ["image/*", "audio/*", "video/*"]
    # 3. For specific file extensions:
    #    accept = { "application/octet-stream" = [".xyz", ".pdb"] }
    # Note: Using "*/*" is not recommended as it may cause browser warnings
    accept = ["*/*"]
    max_files = 20
    max_size_mb = 500

[features.audio]
    # Sample rate of the audio
    sample_rate = 24000

[UI]
# Name of the app and chatbot.
name = "AI Elevator Pitch Tutor"

# Description of the app and chatbot. This is used for HTML tags.
description = "Your personal AI tutor for crafting the perfect elevator pitch"

# Show the readme while the chat is empty.
show_readme_as_default = true

# Hide the chain of thought toggle.
hide_cot = true

# Input placeholder
input_placeholder = "Type your message here..."

# Chain of Thought (CoT) display mode. Can be "hidden", "tool_call" or "full".
cot = "full"

# Specify a CSS file that can be used to customize the user interface.
# The CSS file can be served from the public directory or via an external link.
custom_css = "/static/custom.css"

# Specify a Javascript file that can be used to customize the user interface.
# The Javascript file can be served from the public directory.
custom_js = "/static/custom.js"

# Specify a custom meta image url.
# custom_meta_image_url = "https://chainlit-cloud.s3.eu-west-3.amazonaws.com/logo/chainlit_banner.png"

# Specify a custom build directory for the frontend.
# This can be used to customize the frontend code.
# Be careful: If this is a relative path, it should not start with a slash.
# custom_build = "./public/build"

# Specify optional one or more custom links in the header.
# [[UI.header_links]]
#     name = "Issues"
#     icon_url = "https://avatars.githubusercontent.com/u/128686189?s=200&v=4"
#     url = "https://github.com/Chainlit/chainlit/issues"

[meta]
generated_by = "2.3.0"
