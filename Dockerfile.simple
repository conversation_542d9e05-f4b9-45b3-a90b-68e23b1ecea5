FROM python:3.11-slim

WORKDIR /app

# Create a simple server script with debugging
RUN echo 'import http.server\nimport socketserver\nimport os\nimport sys\n\n# Print environment variables for debugging\nprint(\"Environment variables:\")\nfor key, value in os.environ.items():\n    print(f\"{key}: {value}\")\n\nport = int(os.getenv(\"PORT\", 8000))\nprint(f\"Starting server on port {port}\")\nsys.stdout.flush()  # Force output to be displayed immediately\n\nclass MyHandler(http.server.SimpleHTTPRequestHandler):\n    def do_GET(self):\n        print(f\"Received request for path: {self.path}\")\n        sys.stdout.flush()\n        if self.path == \"/health\":\n            self.send_response(200)\n            self.send_header(\"Content-type\", \"text/plain\")\n            self.end_headers()\n            self.wfile.write(\"Health check OK\".encode())\n            print(\"Sent health check response\")\n        else:\n            self.send_response(200)\n            self.send_header(\"Content-type\", \"text/html\")\n            self.end_headers()\n            self.wfile.write(f\"<html><head><title>Test Server</title></head><body><h1>Test Server is Running!</h1><p>Environment PORT: {port}</p></body></html>\".encode())\n            print(\"Sent HTML response\")\n        sys.stdout.flush()\n\nprint(f\"Binding to 0.0.0.0:{port}\")\nsys.stdout.flush()\nwith socketserver.TCPServer((\"\", port), MyHandler) as httpd:\n    print(f\"Server started at port {port}\")\n    sys.stdout.flush()\n    httpd.serve_forever()' > /app/simple_server.py

# Set environment variables
ENV PORT=8000
ENV PYTHONUNBUFFERED=1

# Expose the port
EXPOSE 8000

# Run the simple server
CMD ["python", "/app/simple_server.py"]
