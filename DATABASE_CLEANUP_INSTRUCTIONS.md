# 🧹 Railway Database Cleanup Instructions

## 📋 Overview
This document provides step-by-step instructions for cleaning all tables in my Railway PostgreSQL database before my June 2nd, 2025 experiment.

## ⚠️ IMPORTANT WARNINGS
- **BACKUP FIRST**: Always ensure you have backups before cleaning data
- **IRREVERSIBLE**: This action cannot be undone
- **EXPERIMENT READY**: Only clean tables immediately before my experiment

## 🛠️ Method 1: Automated Cleanup Script

### Quick Cleanup (Recommended)
Run the automated cleanup script:
```bash
python clean_database_for_experiment.py
```

This script will:
- ✅ Empty all data tables while preserving structure
- ✅ Reset auto-increment counters
- ✅ Verify cleanup completion
- ✅ Provide cleanup summary

## 🛠️ Method 2: Manual Railway CLI Commands

### Prerequisites
1. Ensure Railway CLI is installed and logged in
2. Navigate to your project directory

### Step-by-Step Manual Cleanup

#### 1. Connect to Railway Database
```bash
railway connect
```

#### 2. Clean Data Tables (Execute in order)
```sql
-- Clean conversation and interaction data
DELETE FROM "Message";
DELETE FROM "Element"; 
DELETE FROM "Feedback";
DELETE FROM "Step";

-- Clean session and evaluation data
DELETE FROM student_sessions;
DELETE FROM pitch_evaluations;

-- Clean thread data (keep User table for admin)
DELETE FROM "Thread";

-- Reset auto-increment sequences
ALTER SEQUENCE student_sessions_id_seq RESTART WITH 1;
ALTER SEQUENCE pitch_evaluations_id_seq RESTART WITH 1;
```

#### 3. Verify Cleanup
```sql
-- Check all tables are empty
SELECT 'Element' as table_name, COUNT(*) as row_count FROM "Element"
UNION ALL
SELECT 'Message', COUNT(*) FROM "Message"
UNION ALL
SELECT 'Feedback', COUNT(*) FROM "Feedback"
UNION ALL
SELECT 'Step', COUNT(*) FROM "Step"
UNION ALL
SELECT 'Thread', COUNT(*) FROM "Thread"
UNION ALL
SELECT 'student_sessions', COUNT(*) FROM student_sessions
UNION ALL
SELECT 'pitch_evaluations', COUNT(*) FROM pitch_evaluations;
```

#### 4. Exit Database Connection
```sql
\q
```

## 🛠️ Method 3: Using External Database Tool

### Using pgAdmin or Similar Tool
1. Connect using Railway external database URL:
   ```
   Host: interchange.proxy.rlwy.net
   Port: 20482
   Database: railway
   Username: postgres
   Password: sEfMFeZQidkaiWfyrEZOGiIRQYKNSOli
   ```

2. Execute the cleanup SQL commands from Method 2

## 📊 Expected Results After Cleanup

### Empty Tables (0 rows each):
- ✅ **Element**: Ready for file uploads
- ✅ **Message**: Ready for conversation tracking
- ✅ **Feedback**: Ready for user feedback
- ✅ **Step**: Ready for interaction steps
- ✅ **Thread**: Ready for session threads
- ✅ **student_sessions**: Ready for student tracking
- ✅ **pitch_evaluations**: Ready for pitch scores

### Preserved Tables:
- ✅ **User**: Keep admin user (1 row)

## 🕐 When to Clean Database

### Recommended Timing:
- **1-2 hours before experiment**: Clean tables
- **Test immediately**: Verify one student interaction works
- **Monitor during experiment**: Check data is being collected

### Pre-Experiment Checklist:
- [ ] Database tables cleaned
- [ ] Test student ID generation works
- [ ] Test message storage works
- [ ] Test feedback buttons work
- [ ] Test file upload works
- [ ] Test pitch evaluation works

## 🆘 Emergency Recovery

### If Something Goes Wrong:
1. **Check Railway logs**: `railway logs`
2. **Verify table structure**: Run verification script
3. **Re-run schema fix**: `python fix_railway_schema.py`
4. **Contact support**: Check Railway dashboard

### Backup Strategy:
- Railway provides automatic backups
- Tables can be recreated using schema fix scripts
- No critical data loss risk (experiment data is new)

## 📞 Support Resources

### Scripts Available:
- `clean_database_for_experiment.py` - Automated cleanup
- `final_verification.py` - Verify database status
- `fix_railway_schema.py` - Restore table structure if needed

### Railway Resources:
- **Dashboard**: https://railway.app/dashboard
- **Database Tab**: Monitor table status
- **Logs Tab**: Check for any errors

## 🎯 Experiment Day Protocol

### Morning of June 2nd, 2025:
1. **Clean database** (1-2 hours before)
2. **Test with one student ID** 
3. **Verify all functionality**
4. **Begin experiment**
5. **Monitor data collection**

### During Experiment:
- Check Railway dashboard periodically
- Monitor table row counts increasing
- Ensure no error messages in logs

### Post-Experiment:
- Export data for analysis
- Keep database for backup
- Document any issues encountered

---

**Remember**: Clean tables = Fresh start for accurate experiment data collection! 🧹✨
