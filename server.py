import http.server
import socketserver
import os
import sys

# Print environment variables for debugging
print("Environment variables:")
for key, value in os.environ.items():
    print(f"{key}: {value}")

# Use Railway-provided PORT or default to 8000 if running locally
port = int(os.environ.get("PORT", 8000))
print(f"PORT environment variable: {os.environ.get('PORT', 'Not set, using default')}")
print(f"Starting server on port {port}")
sys.stdout.flush()

class MyHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        print(f"Received request for path: {self.path}")
        sys.stdout.flush()
        if self.path == "/health":
            self.send_response(200)
            self.send_header("Content-type", "text/plain")
            self.end_headers()
            self.wfile.write("Health check OK".encode())
            print("Sent health check response")
        else:
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            self.wfile.write(f"<html><head><title>Test Server</title></head><body><h1>Test Server is Running!</h1><p>Environment PORT: {port}</p></body></html>".encode())
            print("Sent HTML response")
        sys.stdout.flush()

print(f"Binding to 0.0.0.0:{port}")
sys.stdout.flush()

with socketserver.TCPServer(("0.0.0.0", port), MyHandler) as httpd:
    print(f"Server started at port {port}")
    sys.stdout.flush()
    httpd.serve_forever()
