#!/bin/bash
set -e

# Create and activate virtual environment
python -m venv --copies /opt/venv
source /opt/venv/bin/activate

# Install packages one by one, skipping problematic ones
pip install --upgrade pip

# Read requirements.txt and install packages one by one, skipping problematic ones
while IFS= read -r line || [[ -n "$line" ]]; do
    # Skip comments and empty lines
    if [[ $line == \#* ]] || [[ -z $line ]]; then
        continue
    fi
    
    # Skip problematic packages
    if [[ $line == llamacpp* ]] || [[ $line == ctransformers* ]]; then
        echo "Skipping problematic package: $line"
        continue
    fi
    
    # Install the package
    echo "Installing: $line"
    pip install $line || echo "Failed to install: $line, continuing anyway"
done < requirements.txt

# Install additional packages that might be needed
pip install chainlit
pip install langchain
pip install langchain-openai
pip install asyncpg
pip install bcrypt

echo "Build completed successfully!"
