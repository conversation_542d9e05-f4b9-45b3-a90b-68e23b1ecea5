# custom_data_layer.py

import os
import logging
from datetime import datetime
from typing import Optional

from chainlit.data import Base<PERSON>ata<PERSON><PERSON><PERSON>, ElementDict
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import Column, Integer, String, Text, DateTime

DATABASE_URL = os.getenv("DATABASE_URL")

Base = declarative_base()

class PitchEvaluation(Base):
    __tablename__ = "pitch_evaluations"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(255), nullable=False)
    pitch_text = Column(Text)
    evaluation_score = Column(Integer)
    feedback = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

class CustomDataLayer(BaseDataLayer):
    def __init__(self):
        self.engine = create_async_engine(DATABASE_URL, echo=False, future=True)
        self.SessionLocal = sessionmaker(self.engine, class_=AsyncSession, expire_on_commit=False)

    async def connect(self):
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logging.info("✅ Connected to the database and ensured tables exist")

    async def create_element(self, element: ElementDict):
        async with self.SessionLocal() as session:
            if element["type"] == "evaluation":
                record = PitchEvaluation(
                    student_id=element["student_id"],
                    pitch_text=element["pitch_text"],
                    evaluation_score=element["evaluation_score"],
                    feedback=element["feedback"],
                    created_at=element.get("created_at", datetime.utcnow())
                )
                session.add(record)
                await session.commit()
                logging.info(f"✅ Evaluation saved for student {record.student_id}")
