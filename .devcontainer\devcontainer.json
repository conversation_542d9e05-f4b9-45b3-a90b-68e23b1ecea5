// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
	"name": "Python 3",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"image": "mcr.microsoft.com/devcontainers/python:1-3.11-bullseye",
	"features": {
		"ghcr.io/devcontainers-contrib/features/black:2": {
			"version": "latest"
		},
		"ghcr.io/devcontainers-contrib/features/flake8:2": {
			"version": "latest",
			"plugins": "flake8-black"
		}
	},
	"customizations": {
		// Configure properties specific to VS Code.
		"vscode": {
			// Set *default* container specific settings.json values on container create.
			"extensions": [
				"dbaeumer.vscode-eslint",
				"GitHub.codespaces",
				"GitHub.github-vscode-theme",
				"GitHub.vscode-pull-request-github",
				"ms-python.black-formatter",
				"ms-python.debugpy",
				"ms-python.flake8",
				"ms-python.python",
				"ms-python.vscode-pylance"
			  ] 
		}
	},
	"postCreateCommand": "pip3 install --user -r requirements.txt"
	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.

	// Configure tool-specific properties.
	// "customizations": {},

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
