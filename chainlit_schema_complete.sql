-- Chainlit Database Schema based on official data layer
-- https://github.com/Chainlit/chainlit-datalayer/blob/main/prisma/schema.prisma

-- Thread table
CREATE TABLE IF NOT EXISTS "Thread" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    name TEXT,
    tags TEXT[],
    metadata JSONB DEFAULT '{}'::jsonb,
    "userId" TEXT,
    "userIdentifier" TEXT,
    "feedbackStatus" TEXT,
    "humanFeedback" INTEGER,
    "humanFeedbackComment" TEXT
);

-- Step table
CREATE TABLE IF NOT EXISTS "Step" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "parentId" UUID,
    "threadId" UUID REFERENCES "Thread"(id),
    input TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    name TEXT,
    output TEXT,
    type TEXT,
    "startTime" TIMESTAMP WITHOUT TIME ZONE,
    "endTime" TIMESTAMP WITHOUT TIME ZONE,
    error TEXT,
    feedback INTEGER,
    "feedbackComment" TEXT,
    "showInput" TEXT DEFAULT 'json',
    "isError" BOOLEAN DEFAULT false,
    "language" TEXT,
    "indent" INTEGER DEFAULT 0,
    "waitForAnswer" BOOLEAN DEFAULT false,
    "disableFeedback" BOOLEAN DEFAULT false
);

-- Element table
CREATE TABLE IF NOT EXISTS "Element" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "threadId" UUID REFERENCES "Thread"(id),
    "stepId" UUID REFERENCES "Step"(id),
    type TEXT,
    name TEXT,
    url TEXT,
    display TEXT,
    language TEXT,
    size INTEGER,
    "forId" TEXT,
    mime TEXT,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Message table
CREATE TABLE IF NOT EXISTS "Message" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "threadId" UUID REFERENCES "Thread"(id),
    content TEXT,
    author TEXT,
    "authorIsUser" BOOLEAN,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Feedback table
CREATE TABLE IF NOT EXISTS "Feedback" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "stepId" UUID REFERENCES "Step"(id),
    value INTEGER,
    comment TEXT,
    "fromUser" TEXT
);

-- User table for authentication
CREATE TABLE IF NOT EXISTS "User" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
    username TEXT UNIQUE NOT NULL,
    identifier TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb
);
