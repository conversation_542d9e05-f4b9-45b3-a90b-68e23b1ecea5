FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy necessary files for database setup
COPY fix_railway_schema.py .
COPY setup_chainlit_db_complete.py .
COPY chainlit_schema_complete.sql .
COPY users.yaml .
COPY chainlit.toml .

# Copy application files
COPY solutions/langchain/my_agent_bot.py solutions/langchain/
COPY solutions/langchain/chainlit.md solutions/langchain/
# Also copy chainlit.md to the root directory where <PERSON><PERSON> looks for it by default
COPY solutions/langchain/chainlit.md ./chainlit.md
COPY solutions/langchain/static/custom.css solutions/langchain/static/
COPY solutions/langchain/static/custom.js solutions/langchain/static/

# Create necessary directories
RUN mkdir -p solutions/langchain/.files
RUN mkdir -p solutions/langchain/pages
RUN mkdir -p solutions/langchain/static

# Create an empty .env file (will be overridden by environment variables)
RUN touch solutions/langchain/.env

# Install core dependencies
RUN pip install --no-cache-dir chainlit==2.2.1
RUN pip install --no-cache-dir langchain==0.3.20
RUN pip install --no-cache-dir langchain-openai==0.3.7
RUN pip install --no-cache-dir langchain-community==0.3.19
RUN pip install --no-cache-dir langchain-core==0.3.41
RUN pip install --no-cache-dir asyncpg==0.29.0
RUN pip install --no-cache-dir bcrypt==4.3.0
RUN pip install --no-cache-dir PyPDF2==3.0.1
RUN pip install --no-cache-dir python-docx==1.1.0
RUN pip install --no-cache-dir python-dotenv==1.0.1
RUN pip install --no-cache-dir pydantic==2.9.0
RUN pip install --no-cache-dir SQLAlchemy==2.0.29
RUN pip install --no-cache-dir typing-inspect==0.9.0
RUN pip install --no-cache-dir "typing_extensions>=4.7,<5.0"
RUN pip install --no-cache-dir aiofiles==23.2.1
RUN pip install --no-cache-dir aiohttp==3.9.3
RUN pip install --no-cache-dir fastapi==0.115.3
RUN pip install --no-cache-dir uvicorn==0.25.0

# Set environment variables
ENV PYTHONUNBUFFERED=1
# Authentication is disabled
# ENV CHAINLIT_AUTH_SECRET=your_secret_key_for_authentication

# Expose the port
EXPOSE 8000

# Create a simple health check endpoint
RUN echo 'from fastapi import FastAPI\nfrom fastapi.responses import PlainTextResponse\n\napp = FastAPI()\n\<EMAIL>("/")\nasync def root():\n    return PlainTextResponse("OK")\n\<EMAIL>("/health")\nasync def health():\n    return PlainTextResponse("OK")\n' > /app/health_app.py

# Create a startup script
RUN echo '#!/bin/bash\nset -e\n\necho "Starting health check server on port 8080"\nuvicorn health_app:app --host 0.0.0.0 --port 8080 &\nHEALTH_PID=$!\n\necho "Running database schema fix"\npython fix_railway_schema.py\n\n# Authentication is disabled\n# if [ -z "$CHAINLIT_AUTH_SECRET" ]; then\n#   export CHAINLIT_AUTH_SECRET=$(openssl rand -hex 32)\n#   echo "Generated random CHAINLIT_AUTH_SECRET"\n# fi\n\necho "Starting Chainlit on port $PORT"\nchainlit run solutions/langchain/my_agent_bot.py --host 0.0.0.0 --port $PORT\n' > /app/start.sh && \
    chmod +x /app/start.sh

# Run the application
CMD ["/app/start.sh"]
