#!/usr/bin/env python3
"""
Force clean database by handling foreign key constraints properly
"""
import asyncio
import asyncpg
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

async def force_clean_database():
    """Force clean all tables by handling foreign key constraints."""
    
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(url)
        logging.info("✅ Connected to Railway database")
        
        print("🧹 FORCE CLEANING DATABASE")
        print("=" * 30)
        
        # Clean in correct order to handle foreign key constraints
        print("🗑️ Cleaning pitch_evaluations first (has foreign key to student_sessions)...")
        await conn.execute('DELETE FROM pitch_evaluations;')
        print("✅ Cleaned pitch_evaluations")
        
        print("🗑️ Cleaning student_sessions...")
        await conn.execute('DELETE FROM student_sessions;')
        print("✅ Cleaned student_sessions")
        
        # Reset sequences
        print("🔄 Resetting sequences...")
        try:
            await conn.execute('ALTER SEQUENCE pitch_evaluations_id_seq RESTART WITH 1;')
            print("✅ Reset pitch_evaluations sequence")
        except Exception as e:
            print(f"⚠️ pitch_evaluations sequence: {e}")
        
        # Check if student_sessions has a sequence
        try:
            sequences = await conn.fetch("""
                SELECT sequence_name 
                FROM information_schema.sequences 
                WHERE sequence_name LIKE '%student_sessions%';
            """)
            
            if sequences:
                for seq in sequences:
                    await conn.execute(f'ALTER SEQUENCE {seq["sequence_name"]} RESTART WITH 1;')
                    print(f"✅ Reset {seq['sequence_name']}")
            else:
                print("ℹ️ No student_sessions sequence found (table may use SERIAL)")
        except Exception as e:
            print(f"⚠️ Sequence check: {e}")
        
        # Final verification
        print("\n📊 FINAL VERIFICATION:")
        tables = ['Element', 'Message', 'Feedback', 'Step', 'Thread', 'student_sessions', 'pitch_evaluations']
        
        all_clean = True
        for table in tables:
            try:
                count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table}";')
                status = "✅" if count == 0 else "❌"
                print(f"  {status} {table}: {count} rows")
                if count > 0:
                    all_clean = False
            except Exception as e:
                print(f"  ❌ {table}: Error - {e}")
                all_clean = False
        
        # Check User table
        user_count = await conn.fetchval('SELECT COUNT(*) FROM "User";')
        print(f"  ℹ️ User: {user_count} rows (preserved)")
        
        if all_clean:
            print("\n🎉 DATABASE SUCCESSFULLY CLEANED!")
            print("✅ All data tables are now empty")
            print("✅ Ready for June 2nd experiment")
        else:
            print("\n⚠️ Some tables still have data")
        
        await conn.close()
        return all_clean
        
    except Exception as e:
        logging.error(f"❌ Error during force cleanup: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(force_clean_database())
    if success:
        print("\n🎯 Database is clean and ready for experiment!")
    else:
        print("\n❌ Force cleanup failed")
