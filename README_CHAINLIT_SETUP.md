# Chainlit Setup with Database and Authentication

This guide explains how to set up Chainlit with PostgreSQL database persistence and password authentication.

## Prerequisites

- PostgreSQL installed and running
- Python environment with required packages (asyncpg, bcrypt, dotenv)

## Setup Steps

### 1. Database Configuration

1. Make sure your `.env` file contains the correct database connection string:

```
DATABASE_URL=postgresql://ai_user:your_password@localhost:5432/ai_tutor
```

2. Run the database setup script to create the schema and users:

```
python setup_chainlit_db.py
```

This script:
- Creates the necessary tables with proper camelCase column names
- Creates user accounts for authentication (admin and 10 students)

### 2. Authentication Configuration

The application is configured to use password authentication with the following users:

- Admin: username `admin`, password `admin123`
- Students: usernames `student1` through `student10`, password `student123`

These credentials are stored in `users.yaml` and referenced in `chainlit.toml`.

### 3. Running the Application

Always run the application on port 8001 to ensure proper UI rendering:

```
chainlit run solutions\langchain\my_agent_bot.py --port 8001
```

Then access your application at: http://localhost:8001

## File Structure

- `chainlit_schema.sql` - SQL schema for the database tables
- `setup_chainlit_db.py` - Script to set up the database and create users
- `users.yaml` - User credentials for password authentication
- `chainlit.toml` - Chainlit configuration file
- `.env` - Environment variables for database and storage

## Azure Blob Storage (Optional)

To enable file storage with Azure Blob Storage:

1. Create an Azure Storage account and container
2. Update the `.env` file with your Azure credentials:

```
BUCKET_NAME=my-container
APP_AZURE_STORAGE_ACCOUNT=your-storage-account
APP_AZURE_STORAGE_ACCESS_KEY=your-access-key
APP_AZURE_STORAGE_CONNECTION_STRING=your-connection-string
```

## Troubleshooting

If you encounter database-related errors:

1. Check that PostgreSQL is running
2. Verify your database connection string in `.env`
3. Try resetting and re-initializing the database:
   ```
   python drop_tables.py
   python setup_chainlit_db.py
   ```
4. Check the application logs for specific error messages
