import os
import asyncpg
import chainlit as cl
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@cl.on_chat_start
async def on_chat_start():
    """Initialize the chat session and ensure database tables are properly set up."""
    logger.info("Chat session started")

    # Check and fix the Feedback table
    try:
        # Get database URL - try Railway internal URL first, then fall back to environment variable
        database_url = "postgresql://postgres:<EMAIL>:5432/railway"

        try:
            # Try connecting with Railway internal URL first
            conn = await asyncpg.connect(database_url)
            logger.info("Connected to database using Railway internal URL")
        except Exception as db_error:
            # If that fails, try the environment variable
            logger.warning(f"Failed to connect with Railway internal URL: {db_error}")
            database_url = os.getenv("DATABASE_URL")
            if not database_url:
                logger.warning("DATABASE_URL environment variable not set, skipping Feedback table check")
                return
            conn = await asyncpg.connect(database_url)
            logger.info("Connected to database using DATABASE_URL environment variable")

        # Check if the Feedback table exists
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'Feedback'
            )
        """)

        if table_exists:
            # Check if the 'name' column exists
            name_column_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns
                    WHERE table_name = 'Feedback' AND column_name = 'name'
                )
            """)

            if not name_column_exists:
                # Add the 'name' column to the Feedback table
                logger.info("Adding missing 'name' column to Feedback table...")
                await conn.execute("""
                    ALTER TABLE "Feedback"
                    ADD COLUMN name VARCHAR(255) DEFAULT 'feedback'
                """)
                logger.info("Added 'name' column to Feedback table")

        # Close the connection
        await conn.close()

    except Exception as e:
        logger.error(f"Error checking/fixing Feedback table: {e}")
        # Continue with the session even if database check fails

@cl.on_message
async def on_message(message: cl.Message):
    """Handle incoming messages."""
    logger.info(f"Message received: {message.id}")

@cl.on_feedback
async def on_feedback(feedback: cl.Feedback):
    """Handle user feedback."""
    try:
        # Get student ID from session
        student_id = cl.user_session.get("student_id", "unknown")

        # Log the feedback details for debugging
        logger.info(f"Received feedback: message_id={feedback.message_id}, value={feedback.value}, comment={feedback.comment}")

        # Get database URL - try Railway internal URL first, then fall back to environment variable
        database_url = "postgresql://postgres:<EMAIL>:5432/railway"

        try:
            # Try connecting with Railway internal URL first
            conn = await asyncpg.connect(database_url)
            logger.info("Connected to database using Railway internal URL")
        except Exception as db_error:
            # If that fails, try the environment variable
            logger.warning(f"Failed to connect with Railway internal URL: {db_error}")
            database_url = os.getenv("DATABASE_URL")
            if not database_url:
                raise ValueError("DATABASE_URL environment variable not set")
            conn = await asyncpg.connect(database_url)
            logger.info("Connected to database using DATABASE_URL environment variable")

        # Check if the Feedback table exists
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'Feedback'
            )
        """)

        if not table_exists:
            # Create the Feedback table with the correct schema for Chainlit
            logger.info("Creating Feedback table...")
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS "Feedback" (
                    id UUID PRIMARY KEY,
                    "stepId" UUID,
                    name VARCHAR(255) NOT NULL,
                    value INTEGER,
                    comment TEXT
                )
            """)
            logger.info("Feedback table created successfully")
        else:
            # Check the schema of the Feedback table
            columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'Feedback'
                ORDER BY ordinal_position
            """)

            # Log the current schema
            logger.info("Current Feedback table schema:")
            for col in columns:
                logger.info(f"  - {col['column_name']} ({col['data_type']}, nullable: {col['is_nullable']})")

            # Check if we need to recreate the table
            recreate_table = False

            # Check for required columns
            required_columns = ['id', 'stepId', 'name', 'value', 'comment']
            existing_columns = [col['column_name'] for col in columns]

            missing_columns = [col for col in required_columns if col not in existing_columns]
            if missing_columns:
                logger.info(f"Missing required columns: {missing_columns}")
                recreate_table = True

            # Check if id is UUID type
            id_type = None
            for col in columns:
                if col['column_name'] == 'id':
                    id_type = col['data_type']
                    break

            if id_type != 'uuid':
                logger.info(f"Column 'id' has type {id_type}, but should be uuid")
                recreate_table = True

            if recreate_table:
                logger.info("Recreating the Feedback table with correct schema")

                # Backup existing data if any
                try:
                    backup_data = await conn.fetch('SELECT * FROM "Feedback"')
                    logger.info(f"Backed up {len(backup_data)} rows from Feedback table")
                except Exception as e:
                    logger.warning(f"Could not backup data: {e}")
                    backup_data = []

                # Drop the existing table
                await conn.execute('DROP TABLE "Feedback"')
                logger.info("Dropped existing Feedback table")

                # Create the table with the correct schema
                await conn.execute("""
                    CREATE TABLE "Feedback" (
                        id UUID PRIMARY KEY,
                        "stepId" UUID,
                        name VARCHAR(255) NOT NULL,
                        value INTEGER,
                        comment TEXT
                    )
                """)
                logger.info("Created Feedback table with correct schema")

        # Convert the feedback value to an integer (1 for thumbsup, 0 for thumbsdown)
        value_int = 1 if feedback.value == "thumbsup" else 0

        # Generate a UUID for the feedback id
        import uuid
        feedback_id = str(uuid.uuid4())

        # Insert feedback with the format expected by Chainlit
        await conn.execute("""
            INSERT INTO "Feedback" (
                id,
                "stepId",
                name,
                value,
                comment
            ) VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (id) DO UPDATE
            SET value = $4, comment = $5
        """,
        feedback_id,
        feedback.message_id,  # This is the stepId in Chainlit's schema
        "feedback",  # The required 'name' field
        value_int,   # Convert thumbsup/thumbsdown to integer
        feedback.comment or ""
        )

        # Close the connection
        await conn.close()

        logger.info(f"Feedback saved for message {feedback.message_id} from student {student_id}")
    except Exception as e:
        logger.error(f"Error saving feedback: {e}")
        # Log the full error details
        import traceback
        logger.error(traceback.format_exc())
