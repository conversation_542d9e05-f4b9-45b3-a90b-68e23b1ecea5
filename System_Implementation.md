# System Implementation: AI Tutor App Architecture and Setup

## Overview of the System

The AI Tutor application represents a novel approach to adaptive learning in the domain of elevator pitch creation. The system employs a multi-agent architecture powered by Large Language Models (LLMs) to provide personalized guidance and feedback to students as they develop their elevator pitches. Unlike traditional tutoring systems that follow rigid, predefined pathways, this implementation leverages the emergent capabilities of LLMs to create a more dynamic and responsive learning experience. The primary objective of the application is to guide students through the five fundamental steps of creating an effective elevator pitch while adapting to their individual needs and learning pace.

The system's pedagogical approach is grounded in constructivist learning theory, where knowledge is built through guided interaction rather than direct instruction. This is operationalized through a multi-agent framework where different "personas" (implemented as specialized LLM instances) interact with the student based on their current needs and progress through the learning material. This approach allows for a more natural and engaging learning experience that mimics human tutoring interactions while maintaining consistency in pedagogical approach.

## System Architecture

### Core Frameworks and Technologies

The AI Tutor application is built upon two primary frameworks: LangChain and Chainlit. LangChain provides the foundational architecture for creating and orchestrating the LLM-based agents, while Chainlit offers a robust chat interface and session management capabilities. This combination enables the creation of a sophisticated multi-agent system with minimal boilerplate code, allowing the development focus to remain on pedagogical design rather than technical implementation details.

The application utilizes OpenAI's GPT-4o model via the OpenAI API, selected for its superior instruction-following capabilities, contextual understanding, and reduced hallucination compared to earlier models. The GPT-4o model represents a balance between performance and cost-efficiency, offering capabilities comparable to GPT-4 Turbo at a lower computational cost, which is an important consideration for educational applications where budget constraints are often significant.

### Multi-Agent Architecture

The system implements four distinct agent roles, each with specialized prompts and responsibilities:

1. **Mentor Agent**: Serves as the primary instructional guide, providing structured feedback and leading students through the five steps of elevator pitch creation. This agent is responsible for determining when a student has sufficiently mastered each step and can progress to the next.

2. **Peer Agent**: Offers a more casual, collaborative perspective, designed to reduce anxiety and encourage creative exploration. This agent uses less formal language and presents as a fellow student rather than an authority figure.

3. **Evaluator Agent**: Provides summative assessment of completed elevator pitches, offering both quantitative scores and qualitative feedback across multiple dimensions including clarity, engagement, persuasiveness, structure, and effectiveness.

4. **Progress Agent**: Tracks and reports on student progress through the learning material, providing metacognitive support by helping students understand their own learning journey.

Each agent is implemented as a specialized instance of the LLM with a unique system prompt that defines its role, communication style, and objectives. This approach allows for consistent agent behavior while leveraging the same underlying model architecture.

### Message Routing and Agent Selection

The system implements a flexible message routing system that allows students to explicitly select which agent they wish to interact with using simple commands (e.g., `/mentor`, `/peer`, `/progress`, `/eval`). This design choice empowers students to access different types of support based on their current needs, rather than forcing them through a predetermined sequence of interactions.

The default interaction path begins with the Mentor Agent, which guides students through the structured learning process. However, students can switch to the Peer Agent when they need more collaborative brainstorming, consult the Progress Agent to understand their advancement through the material, or utilize the Evaluator Agent when they wish to receive assessment on their work.

This agent selection strategy represents a balance between structured guidance and learner autonomy, allowing the system to adapt to different learning preferences and needs while maintaining pedagogical coherence.

## Frontend and User Interaction

### Chainlit Interface

The frontend of the AI Tutor application is built using Chainlit, which provides a clean, intuitive chat interface that resembles familiar messaging applications. This design choice reduces the cognitive load associated with learning a new interface, allowing students to focus on the content of their learning rather than the mechanics of the application.

The interface supports markdown formatting, enabling rich text responses from the agents that can include emphasis, lists, and structured content. This capability is leveraged to create visually distinct responses that highlight important information and organize complex feedback in an accessible manner.

### User Onboarding and Identification

The system implements a lightweight onboarding process that assigns each student a unique 8-digit identifier upon their first interaction with the application. This identifier serves as a pseudo-anonymous way to track student progress across sessions without requiring formal authentication. Students are encouraged to note their identifier if they wish to resume their session at a later time.

This approach balances the need for session persistence with privacy considerations and ease of use, particularly important in educational research contexts where minimizing barriers to participation is crucial.

### File Upload Capabilities

The application supports document upload functionality, allowing students to submit their completed elevator pitches in PDF or DOCX format for evaluation. This feature enables the Evaluator Agent to analyze longer-form content that might be difficult to type directly into the chat interface, and supports the assessment of pitches that students may have developed using external tools.

The file processing system extracts text content from these documents using PyPDF2 for PDF files and python-docx for DOCX files, making the content available to the Evaluator Agent for analysis and feedback.

## Backend and Data Logging

### Database Architecture

The application utilizes a PostgreSQL database for persistent storage of student interactions and progress. This relational database was selected for its robustness, ACID compliance, and strong support for JSON data types, which are particularly useful for storing the semi-structured data generated during LLM interactions.

The database schema includes several key tables:

1. **student_sessions**: Stores core session data including student identifiers, current progress (step index), total interactions, completed steps, and timestamps for session creation and last update.

2. **pitch_evaluations**: Records evaluation data including the student identifier, evaluation timestamp, step name, numerical score, and textual feedback.

3. **Thread**, **Message**, **Step**, and **Feedback**: Standard Chainlit tables that store the detailed interaction history, enabling potential future analysis of conversation patterns and learning trajectories.

This database design enables both operational functionality (session persistence and progress tracking) and research capabilities (analysis of learning patterns and system effectiveness).

### Asynchronous Database Interactions

The application implements fully asynchronous database interactions using asyncpg, a high-performance PostgreSQL client library for Python. This approach ensures that database operations do not block the main application thread, maintaining responsive user interactions even during database writes or complex queries.

Key database functions include:

- `save_session_to_db`: Updates or creates student session records with current progress information
- `save_pitch_evaluation`: Stores evaluation results from the Evaluator Agent
- `increment_interactions`: Tracks the number of exchanges between the student and agents

These functions implement robust error handling and connection management, with fallback mechanisms to ensure system stability even when database operations fail.

## Deployment Setup

### Railway Platform Deployment

The AI Tutor application is deployed on Railway.app, a modern platform-as-a-service (PaaS) provider that offers integrated PostgreSQL database hosting alongside application deployment. This unified deployment approach simplifies the operational complexity of maintaining separate application and database services.

The deployment configuration utilizes Docker containerization to ensure consistency between development and production environments. The application container includes all necessary dependencies and is configured to automatically connect to the PostgreSQL instance using internal networking, enhancing both security and performance.

### Environment Configuration

Sensitive configuration values, particularly the OpenAI API key, are managed through environment variables stored securely in the Railway platform. This approach prevents credentials from being committed to the codebase while making them available to the application at runtime.

Local development utilizes a `.env` file for environment variables, while the Chainlit-specific configuration is managed through a `config.toml` file that defines application behavior, authentication settings, and UI customization. This separation of concerns allows for environment-specific configuration without code changes.

### Testing Environments

During development, the application was tested in multiple environments:

1. **Local Development**: Using localhost with a local PostgreSQL instance for rapid iteration
2. **Staging Environment**: Deployed to Railway with a separate database instance for integration testing
3. **Production Environment**: The final deployment environment used for the research study

This multi-environment approach allowed for thorough testing of both functionality and performance under various conditions before exposing the application to research participants.

## Performance Considerations

### Response Streaming

The application implements response streaming for all agent interactions, displaying tokens to the user as they are generated rather than waiting for the complete response. This approach significantly improves the perceived responsiveness of the system, particularly important for educational applications where maintaining student engagement is crucial.

The streaming implementation utilizes LangChain's callback handlers in conjunction with Chainlit's streaming support, creating a seamless experience where students can begin reading and processing agent responses while the remainder is still being generated.

### Cost and Rate Limit Management

To manage API costs and rate limits, the application implements several optimization strategies:

1. **Model Selection**: Utilizing GPT-4o instead of GPT-4 Turbo reduces token costs while maintaining comparable performance
2. **Prompt Engineering**: Carefully designed prompts minimize token usage while maximizing instructional effectiveness
3. **Caching**: Implementing response caching for frequently accessed content to reduce redundant API calls
4. **Batched Operations**: Combining database operations where possible to reduce connection overhead

These optimizations ensure that the application remains cost-effective even when scaling to support multiple simultaneous users, an important consideration for educational technology with limited budgets.

## Limitations and Scalability

### Current Limitations

While the AI Tutor application represents a significant advancement in adaptive learning technology, several limitations should be acknowledged:

1. **Session-Based Memory**: The current implementation maintains memory only within a single session, without sophisticated long-term memory capabilities that could enable more personalized instruction across multiple sessions.

2. **Limited Analytics**: While basic interaction data is captured, the system does not yet implement comprehensive analytics to identify patterns in student learning or agent effectiveness.

3. **Controlled Environment Assumptions**: The current design assumes operation in controlled classroom settings with reliable internet connectivity and modern web browsers.

### Scalability Considerations

The application architecture supports horizontal scaling to accommodate increased user load through several design choices:

1. **Stateless Application Layer**: Core application logic is stateless, with all persistent state maintained in the database
2. **Asynchronous Processing**: Non-blocking I/O operations enable efficient resource utilization
3. **Containerized Deployment**: Docker containerization facilitates deployment across multiple instances

These architectural decisions position the system well for potential expansion beyond the initial research context, though additional development would be required to address the limitations noted above for large-scale educational deployment.

In conclusion, the AI Tutor application represents a carefully designed implementation of LLM-based educational technology, balancing pedagogical objectives with technical constraints to create an effective adaptive learning environment. The multi-agent architecture, coupled with robust data persistence and a user-friendly interface, creates a foundation for both the current research study and potential future developments in AI-assisted education.
