import os
import asyncpg
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def init_chainlit_tables():
    """Initialize the required tables for Chainlit data persistence."""
    load_dotenv()

    try:
        # Connect to the database
        conn = await asyncpg.connect(os.getenv("DATABASE_URL"))

        # Create Thread table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Thread" (
                id UUID PRIMARY KEY,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                name TEXT,
                tags TEXT[],
                metadata JSONB DEFAULT '{}'::jsonb,
                user_id TEXT,
                user_identifier TEXT,
                feedback_status TEXT,
                human_feedback INTEGER,
                human_feedback_comment TEXT
            )
        """)

        # Create Step table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Step" (
                id UUID PRIMARY KEY,
                thread_id UUID REFERENCES "Thread"(id),
                parent_id UUID,
                name TEXT,
                type TEXT,
                input TEXT,
                output TEXT,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                start_time TIMESTAMP WITHOUT TIME ZONE,
                end_time TIMESTAMP WITHOUT TIME ZONE,
                metadata JSONB DEFAULT '{}'::jsonb,
                error TEXT,
                feedback INTEGER,
                feedback_comment TEXT
            )
        """)

        # Create Element table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Element" (
                id UUID PRIMARY KEY,
                thread_id UUID REFERENCES "Thread"(id),
                step_id UUID REFERENCES "Step"(id),
                type TEXT,
                name TEXT,
                url TEXT,
                display TEXT,
                language TEXT,
                size INTEGER,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                for_id TEXT,
                mime TEXT,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        """)

        # Create Message table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS "Message" (
                id UUID PRIMARY KEY,
                thread_id UUID REFERENCES "Thread"(id),
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc'),
                content TEXT,
                author TEXT,
                author_is_user BOOLEAN,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        """)

        logging.info("✅ Chainlit database tables created successfully")

        # Close the connection
        await conn.close()

    except Exception as e:
        logging.error(f"❌ Chainlit database initialization failed: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(init_chainlit_tables())
