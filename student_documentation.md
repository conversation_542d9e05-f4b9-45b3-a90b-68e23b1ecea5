# AI Elevator Pitch Tutor - Student Guide

## Introduction

Welcome to the AI Elevator Pitch Tutor! This guide will help you understand how to use the application effectively during the classroom experiment.

## What is an Elevator Pitch?

An elevator pitch is a brief, persuasive speech used to spark interest in a project, idea, product, or yourself. It should be concise enough to deliver during a short elevator ride (hence the name), typically 30-60 seconds.

A good elevator pitch quickly and clearly communicates:
- Who your target audience is
- What problem you're solving
- How your solution works
- What makes your solution unique
- What action you want the listener to take

## The AI Tutor System

The AI Elevator Pitch Tutor features four specialized AI agents to help you develop your pitch:

1. **Mentor Agent** - Provides structured guidance through each step of creating your pitch
2. **Peer Agent** - Offers casual feedback like a fellow student would
3. **Progress Agent** - Tracks your progress through the pitch development process
4. **Evaluator Agent** - Reviews and scores your completed pitch

## How to Access the Application

1. Open your web browser
2. Go to: https://ai-agent-production-d1cc.up.railway.app/
3. You'll see the welcome screen with instructions

## Using the Application

### Getting Started

1. Read the welcome instructions
2. You'll start with the Mentor Agent by default
3. Begin by telling the Mentor Agent about your idea or product

### Switching Between Agents

Type these commands in the chat to switch between agents:
- `/mentor` - Switch to the Mentor Agent
- `/peer` - Switch to the Peer Agent
- `/progress` - Switch to the Progress Agent
- `/eval` - Switch to the Evaluator Agent

### The Five-Step Process

The Mentor Agent will guide you through these five steps:

1. **Identify the Target Audience**
   - Who would benefit most from your idea?
   - What are their characteristics, needs, and pain points?

2. **Define the Problem/Need**
   - What specific problem does your idea solve?
   - Why is this problem important to solve?

3. **Introduce the Product/Service**
   - What is your solution to the problem?
   - How does it work at a high level?

4. **Highlight the Key Differentiator**
   - What makes your idea unique or better than alternatives?
   - Why should someone choose your solution over others?

5. **End with a Strong Closing Statement**
   - What action do you want your audience to take?
   - How can they get involved or learn more?

### Submitting Your Pitch for Evaluation

When you've completed your pitch:

1. Write your final pitch in a document (Word or PDF)
2. In the chat, type `/upload` or click the 📎 button
3. Upload your document
4. The Evaluator Agent will review your pitch and provide:
   - A score out of 10
   - Specific feedback on strengths and areas for improvement
   - Suggestions for enhancement

## Tips for Success

- **Be specific and concise** - Avoid vague statements and unnecessary details
- **Use simple language** - Avoid jargon and technical terms unless necessary
- **Focus on benefits** - Explain how your solution helps people, not just what it does
- **Practice your delivery** - A good pitch sounds natural, not memorized
- **Show enthusiasm** - Your excitement about your idea should come through
- **Iterate based on feedback** - Use the AI agents' suggestions to improve your pitch

## Example Elevator Pitch

```
Hello, I'm Sarah. I've developed EcoTrack, a mobile app for environmentally-conscious consumers who struggle to find products that align with their values.

Unlike existing eco-rating apps, EcoTrack provides personalized sustainability scores based on your specific priorities through our proprietary algorithm that analyzes over 50 environmental factors.

Would you be interested in joining our beta testing program to help refine the experience before our public launch next month?
```

## Troubleshooting

If you encounter any issues:
- Try refreshing the page
- If an agent stops responding, switch to another agent and then back
- For file upload issues, ensure your document is in PDF or DOCX format and under 20MB

## Experiment Goals

During this classroom experiment, we're evaluating:
1. How effectively the AI agents help you develop your pitch
2. The quality of feedback provided
3. Your overall experience with the system

Your honest engagement with the system will help improve it for future students!

## Questions?

If you have any questions during the experiment, please ask your instructor for assistance.

Good luck with your elevator pitch!
