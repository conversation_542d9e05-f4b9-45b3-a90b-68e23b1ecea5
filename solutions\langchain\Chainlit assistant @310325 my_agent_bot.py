import os
import re
import uuid
import logging
import asyncpg
from datetime import datetime
from dotenv import load_dotenv
from docx import Document
import PyPDF2

import chainlit as cl
from chainlit.context import context
from chainlit.types import AskFileResponse
from chainlit.user_session import get_user_session
from chainlit.data_persistence import BaseDataLayer
from chainlit.data_persistence import sync_run

from langchain_core.prompts import PromptTemplate
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.callbacks import BaseCallbackHandler

from langchain_openai import ChatOpenAI

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# --- Database Setup ---
async def create_db_schema(data_layer: BaseDataLayer):
    """Create the database schema if it doesn't exist."""
    try:
        async with data_layer.session() as session:
            await session.execute(
                """
                CREATE TABLE IF NOT EXISTS pitch_evaluations (
                    id SERIAL PRIMARY KEY,
                    student_id VARCHAR(255) NOT NULL,
                    pitch_text TEXT,
                    evaluation_score INTEGER,
                    feedback TEXT,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() at time zone 'utc')
                )
                """
            )
            await session.commit()
        logging.info("✅ Database schema created/verified")
    except Exception as e:
        logging.error(f"❌ Error creating database schema: {e}")

# --- Utility Functions ---
def extract_text_from_file(file: cl.File):
    """Extract text from PDF or DOCX file."""
    try:
        if file.name.endswith(".pdf"):
            pdf_reader = PyPDF2.PdfReader(file.path)
            text = "\n".join([page.extract_text() for page in pdf_reader.pages if page.extract_text()])
        elif file.name.endswith(".docx"):
            doc = Document(file.path)
            text = "\n".join([para.text for para in doc.paragraphs])
        else:
            logging.warning(f"❌ Unsupported file type: {file.name}")
            return None
        return text
    except Exception as e:
        logging.error(f"❌ Error extracting text from {file.name}: {e}")
        return None

def flatten_messages(messages: list[BaseMessage]):
    """Safely converts list of messages to a formatted string."""
    return "\n".join([f"{m.type}: {m.content}" for m in messages]) if isinstance(messages, list) and all(isinstance(m, BaseMessage) for m in messages) else str(messages)

# --- LLM Setup ---
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

llm = ChatOpenAI(model="gpt-4o", temperature=0.7, openai_api_key=api_key, streaming=True)

class StreamHandler(BaseCallbackHandler):
    """Callback handler for streaming LLM responses."""
    def __init__(self, message_id: str):
        self.message_id = message_id
        self.content = ""

    async def on_llm_new_token(self, token: str, **kwargs):
        self.content += token
        await cl.Message(id=self.message_id, content=self.content).update()

    async def on_llm_error(self, error: BaseException, **kwargs):
        await cl.Message(id=self.message_id, content=f"Error: {error}").update()

# --- Define AI Agents ---
PROMPTS = {
    "mentor": PromptTemplate(
        input_variables=["context", "question", "current_step"],
        template="""[SYSTEM MESSAGE]
        You are the Mentor Agent, guiding students through creating an elevator pitch. 
        🚀 **Current Step:** {current_step} - NEVER skip a step in the process.
        Ask questions that ensure students fully understand each step before moving forward. Challenge their responses and encourage reflection. If they try to skip ahead, redirect them to the current step.
        **Current Progress:** {context}
        **Student Question:** {question}
        **Mentor's Guided Response:**"""
    ),
    "peer": PromptTemplate(
        input_variables=["context", "question"],
        template="""[SYSTEM MESSAGE]
        You are the Peer Agent, just a regular student helping another student brainstorm an elevator pitch. Keep the conversation chill, friendly, and informal. You are NOT an expert—don’t give structured frameworks or detailed feedback. Instead, react naturally and ask simple, curious questions. If the user asks "Who are you?", respond: "Hey! I'm your Peer Agent, just a fellow student here to bounce ideas with you. No pressure, let’s figure this out together!"
        **Conversation History:** {context}
        **Student Message:** {question}
        **Peer’s Thoughtful Response:**"""
    ),
    "progress": PromptTemplate(
        input_variables=["context", "question", "student_progress", "time_spent"],
        template="""[SYSTEM MESSAGE]
        You are the Progress Agent, tracking the student’s progress through their elevator pitch creation. Track student step progress and time spent. Retrieve stored progress from the database.
        ✅ **Current Step:** {student_progress} ⏳ **Time Spent on This Step:** {time_spent} seconds
        **Conversation History:** {context}
        **Student Message:** {question}
        **Progress Tracking Response:**"""
    ),
    "eval": PromptTemplate(
        input_variables=["pitch"],
        template="""[SYSTEM MESSAGE]
        You are the Evaluator Agent, responsible for reviewing and grading elevator pitches. Score each pitch out of 10 based on: 
        1️⃣ **Clarity** 2️⃣ **Engagement** 3️⃣ **Persuasiveness** 4️⃣ **Structure** 5️⃣ **Effectiveness** 
        Provide structured feedback on strengths & areas for improvement.
        **Student Pitch:** {pitch}
        **Evaluation Feedback & Score:**"""
    )
}

agents = {agent: PROMPTS[agent] | llm for agent in PROMPTS.keys()}

# --- Chainlit App ---
@cl.on_chat_start
async def start():
    """Initialize chat session and welcome message."""
    data_layer: Optional[BaseDataLayer] = cl.data_layer
    if data_layer:
        await create_db_schema(data_layer)
    else:
        logging.warning("⚠️ Data layer not found, skipping schema creation.")

    cl.user_session.set("agent", "mentor")
    cl.user_session.set("student_id", f"student_{uuid.uuid4().hex[:8]}")
    cl.user_session.set("pitch_steps", pitch_steps)  # Store pitch_steps in user session
    
    welcome_message = """🎓 **Welcome to the AI Elevator Pitch Tutor!** 🏆
    🤖 This tutor is powered by **four AI agents**:
    - 🧑‍🏫 **Mentor Agent** → Guides you step-by-step.
    - 👥 **Peer Agent** → Thinks with you like a fellow student.
    - 📈 **Progress Agent** → Tracks your step-by-step progress.
    - 📝 **Evaluator Agent** → Reviews and scores your final pitch.
    💡 **Type `/upload` when you're ready to submit your pitch.** You can also use the 📎 button to attach a `.pdf` or `.docx`.
    🔄 **Switch Agents Anytime**: - `/mentor`, `/peer`, `/progress`, `/eval`
    🔹 You're now talking to the **Mentor Agent**.
    """
    await cl.Message(content=welcome_message).send()

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages and agent interactions."""
    selected_agent = cl.user_session.get("agent")
    student_id = cl.user_session.get("student_id")
    pitch_steps = cl.user_session.get("pitch_steps")  # Retrieve pitch_steps

    if message.content.lower() == "/upload":
        files = await cl.AskFileMessage(
            content="Please upload your pitch document (PDF or DOCX)",
            accept=["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
            max_size_mb=20,
            timeout=180,
        ).send()
        if files:
            await process_file(files[0])
        return

    if message.content.lower() in ["/mentor", "/peer", "/progress", "/eval"]:
        new_agent = message.content.lower()[1:]
        cl.user_session.set("agent", new_agent)
        response = f"🔹 Switched to **{new_agent.capitalize()} Agent**"
        await cl.Message(content=response).send()
        return

    try:
        msg = cl.Message(content="")
        await msg.send()

        callback = StreamHandler(msg.id)
        llm_with_callback = llm.bind(callbacks=[callback])
        chain = agents[selected_agent] | llm_with_callback

        chain_input = {
            "context": flatten_messages(cl.user_session.get(f"{selected_agent}_memory", ChatMessageHistory()).messages),
            "question": message.content,
            "current_step": cl.user_session.get("current_step", "initial"),
            "student_progress": cl.user_session.get("student_progress", "Not started"),
            "time_spent": cl.user_session.get("time_spent", 0)
        }

        response = await chain.ainvoke(chain_input)
        ai_text = response.content

        await msg.update(content=ai_text)

        agent_memory: ChatMessageHistory = cl.user_session.get(f"{selected_agent}_memory", ChatMessageHistory())
        agent_memory.add_message(AIMessage(content=ai_text))
        cl.user_session.set(f"{selected_agent}_memory", agent_memory)

        try:
            async with cl.Step(name=cl.user_session.get("current_step", "initial"), type=f"{selected_agent}_interaction", input=message.content, output=ai_text) as step:
                pass
        except Exception as db_error:
            logging.error(f"Database operation failed: {db_error}")

    except Exception as e:
        logging.error(f"❌ Error in message processing: {e}")
        await cl.Message(content=f"An error occurred: {e}").send()

async def process_file(file: cl.File):
    """Process uploaded file, extract text, and evaluate pitch."""
    pitch_text = extract_text_from_file(file)
    student_id = cl.user_session.get("student_id")

    if not pitch_text:
        await cl.Message(content="❌ Couldn't extract text. Please upload a valid `.pdf` or `.docx`.").send()
        return

    try:
        response = await agents["eval"].ainvoke({"pitch": pitch_text})
        feedback = response.content

        match = re.search(r"[Ss]core[:\s]+(\d{1,2})\s*/\s*10", feedback)
        score = int(match.group(1)) if match else None

        # Use Chainlit's data layer to save evaluation
        data_layer: Optional[BaseDataLayer] = cl.data_layer
        if data_layer:
            try:
                await data_layer.create_element(
                    {
                        "type": "evaluation",
                        "student_id": student_id,
                        "pitch_text": pitch_text,
                        "evaluation_score": score,
                        "feedback": feedback,
                        "created_at": datetime.utcnow(),
                    }
                )
                logging.info(f"✅ Evaluation saved to database for student {student_id}")
            except Exception as db_error:
                logging.error(f"❌ Database operation failed: {db_error}")
        else:
            logging.warning("⚠️ Data layer not found, skipping evaluation save.")

        await cl.Message(content=f"✅ Evaluation Complete:\n\n{feedback}").send()

    except Exception as e:
        logging.error(f"❌ Evaluation Error: {e}")
        await cl.Message(content="⚠️ Error during pitch evaluation. Please try again.").send()

@cl.on_stop
async def stop_execution():
    """Clear user session on stop."""
    cl.user_session.clear()
    await cl.Message("Session stopped. Your progress is saved!").send()