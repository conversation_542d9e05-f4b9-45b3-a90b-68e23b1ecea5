#!/usr/bin/env python3
"""
Clean all database tables for June 2nd, 2025 experiment
This script empties all data tables while preserving structure
"""
import asyncio
import asyncpg
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def clean_database_for_experiment():
    """Clean all tables for fresh experiment start."""
    
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(url)
        logging.info("✅ Connected to Railway database")
        
        print("🧹 CLEANING DATABASE FOR JUNE 2ND EXPERIMENT")
        print("=" * 50)
        print("⚠️  WARNING: This will delete ALL data from tables!")
        print("📅 Experiment Date: June 2nd, 2025")
        print()
        
        # Get current table counts before cleanup
        print("📊 CURRENT TABLE STATUS (BEFORE CLEANUP):")
        tables_to_clean = ['Element', 'Message', 'Feedback', 'Step', 'Thread', 'student_sessions', 'pitch_evaluations']
        
        before_counts = {}
        for table in tables_to_clean:
            try:
                count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table}";')
                before_counts[table] = count
                print(f"  - {table}: {count} rows")
            except Exception as e:
                before_counts[table] = "Error"
                print(f"  - {table}: Error - {e}")
        
        print()
        
        # Confirm cleanup
        response = input("🤔 Are you sure you want to clean all tables? Type 'YES' to continue: ")
        if response != "YES":
            print("❌ Cleanup cancelled by user")
            await conn.close()
            return False
        
        print("\n🧹 Starting database cleanup...")
        
        # 1. Clean conversation and interaction data
        logging.info("🗑️ Cleaning conversation data...")
        try:
            await conn.execute('DELETE FROM "Message";')
            logging.info("✅ Cleaned Message table")
        except Exception as e:
            logging.error(f"❌ Error cleaning Message table: {e}")
        
        try:
            await conn.execute('DELETE FROM "Element";')
            logging.info("✅ Cleaned Element table")
        except Exception as e:
            logging.error(f"❌ Error cleaning Element table: {e}")
        
        try:
            await conn.execute('DELETE FROM "Feedback";')
            logging.info("✅ Cleaned Feedback table")
        except Exception as e:
            logging.error(f"❌ Error cleaning Feedback table: {e}")
        
        try:
            await conn.execute('DELETE FROM "Step";')
            logging.info("✅ Cleaned Step table")
        except Exception as e:
            logging.error(f"❌ Error cleaning Step table: {e}")
        
        # 2. Clean session and evaluation data
        logging.info("🗑️ Cleaning session and evaluation data...")
        try:
            await conn.execute('DELETE FROM student_sessions;')
            logging.info("✅ Cleaned student_sessions table")
        except Exception as e:
            logging.error(f"❌ Error cleaning student_sessions table: {e}")
        
        try:
            await conn.execute('DELETE FROM pitch_evaluations;')
            logging.info("✅ Cleaned pitch_evaluations table")
        except Exception as e:
            logging.error(f"❌ Error cleaning pitch_evaluations table: {e}")
        
        # 3. Clean thread data
        logging.info("🗑️ Cleaning thread data...")
        try:
            await conn.execute('DELETE FROM "Thread";')
            logging.info("✅ Cleaned Thread table")
        except Exception as e:
            logging.error(f"❌ Error cleaning Thread table: {e}")
        
        # 4. Reset auto-increment sequences
        logging.info("🔄 Resetting auto-increment sequences...")
        try:
            await conn.execute('ALTER SEQUENCE student_sessions_id_seq RESTART WITH 1;')
            logging.info("✅ Reset student_sessions sequence")
        except Exception as e:
            logging.warning(f"⚠️ Could not reset student_sessions sequence: {e}")
        
        try:
            await conn.execute('ALTER SEQUENCE pitch_evaluations_id_seq RESTART WITH 1;')
            logging.info("✅ Reset pitch_evaluations sequence")
        except Exception as e:
            logging.warning(f"⚠️ Could not reset pitch_evaluations sequence: {e}")
        
        # 5. Verify cleanup
        print("\n📊 VERIFICATION (AFTER CLEANUP):")
        all_clean = True
        
        for table in tables_to_clean:
            try:
                count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table}";')
                status = "✅" if count == 0 else "❌"
                print(f"  {status} {table}: {count} rows")
                if count > 0:
                    all_clean = False
            except Exception as e:
                print(f"  ❌ {table}: Error - {e}")
                all_clean = False
        
        # Check User table (should be preserved)
        try:
            user_count = await conn.fetchval('SELECT COUNT(*) FROM "User";')
            print(f"  ℹ️ User: {user_count} rows (preserved)")
        except Exception as e:
            print(f"  ⚠️ User: Error - {e}")
        
        print()
        
        # 6. Summary
        if all_clean:
            print("🎉 DATABASE CLEANUP COMPLETED SUCCESSFULLY!")
            print("✅ All data tables are now empty")
            print("✅ Table structures preserved")
            print("✅ Auto-increment sequences reset")
            print("✅ Ready for June 2nd experiment")
            
            print(f"\n📊 CLEANUP SUMMARY:")
            total_rows_removed = sum(count for count in before_counts.values() if isinstance(count, int))
            print(f"  - Total rows removed: {total_rows_removed}")
            print(f"  - Tables cleaned: {len(tables_to_clean)}")
            print(f"  - Cleanup time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        else:
            print("⚠️ CLEANUP INCOMPLETE!")
            print("Some tables still contain data. Check the verification above.")
            print("You may need to run the cleanup again or check for foreign key constraints.")
        
        await conn.close()
        return all_clean
        
    except Exception as e:
        logging.error(f"❌ Error during database cleanup: {e}")
        return False

async def test_database_functionality():
    """Test that database is ready for experiment."""
    
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(url)
        print("\n🧪 TESTING DATABASE FUNCTIONALITY...")
        
        # Test student session creation
        test_student_id = "TEST12345"
        await conn.execute("""
            INSERT INTO student_sessions (student_id, current_step_index, total_interactions)
            VALUES ($1, $2, $3);
        """, test_student_id, 0, 0)
        print("✅ Student session creation: WORKING")
        
        # Test message insertion
        test_thread_id = await conn.fetchval("""
            INSERT INTO "Thread" (name, "userIdentifier")
            VALUES ($1, $2)
            RETURNING id;
        """, "Test Thread", test_student_id)
        
        await conn.execute("""
            INSERT INTO "Message" ("threadId", content, author, "authorIsUser")
            VALUES ($1, $2, $3, $4);
        """, test_thread_id, "Test message", "Test User", True)
        print("✅ Message storage: WORKING")
        
        # Test feedback insertion
        await conn.execute("""
            INSERT INTO "Feedback" (name, value, comment)
            VALUES ($1, $2, $3);
        """, "test_feedback", 5, "Test feedback")
        print("✅ Feedback storage: WORKING")
        
        # Clean up test data
        await conn.execute('DELETE FROM "Feedback" WHERE name = $1;', "test_feedback")
        await conn.execute('DELETE FROM "Message" WHERE "threadId" = $1;', test_thread_id)
        await conn.execute('DELETE FROM "Thread" WHERE id = $1;', test_thread_id)
        await conn.execute('DELETE FROM student_sessions WHERE student_id = $1;', test_student_id)
        print("✅ Test data cleanup: COMPLETED")
        
        print("🎯 Database is ready for experiment!")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database functionality test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Database Cleanup for June 2nd Experiment...")
    print(f"📅 Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run cleanup
    cleanup_success = asyncio.run(clean_database_for_experiment())
    
    if cleanup_success:
        # Test functionality
        test_success = asyncio.run(test_database_functionality())
        
        if test_success:
            print("\n🎉 READY FOR EXPERIMENT!")
            print("✅ Database cleaned and tested")
            print("✅ All functionality verified")
            print("🎯 You can now start your June 2nd experiment with clean data!")
        else:
            print("\n⚠️ Cleanup completed but functionality test failed")
            print("Please check the database configuration before starting experiment")
    else:
        print("\n❌ Database cleanup failed")
        print("Please check the error messages above and try again")
