#!/usr/bin/env python3
"""
Debug Chainlit data persistence and check why messages aren't being stored
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def debug_chainlit_persistence():
    """Debug why Chainlit isn't storing messages automatically."""
    
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(url)
        print("🔍 DEBUGGING CHAINLIT DATA PERSISTENCE")
        print("=" * 50)
        
        # 1. Check if Chainlit tables have the correct schema
        print("📋 Checking Chainlit table schemas...")
        
        # Check Message table schema
        message_schema = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Message' AND table_schema = 'public'
            ORDER BY ordinal_position;
        """)
        
        print("\n🔍 MESSAGE TABLE SCHEMA:")
        expected_columns = ['id', 'createdAt', 'updatedAt', 'threadId', 'content', 'author', 'authorIsUser', 'metadata']
        actual_columns = [col['column_name'] for col in message_schema]
        
        for col in message_schema:
            status = "✅" if col['column_name'] in expected_columns else "⚠️"
            print(f"  {status} {col['column_name']} ({col['data_type']}) - Default: {col['column_default']}")
        
        missing_columns = [col for col in expected_columns if col not in actual_columns]
        if missing_columns:
            print(f"\n❌ MISSING COLUMNS: {missing_columns}")
        else:
            print(f"\n✅ All expected columns present")
        
        # Check Thread table schema
        thread_schema = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Thread' AND table_schema = 'public'
            ORDER BY ordinal_position;
        """)
        
        print("\n🔍 THREAD TABLE SCHEMA:")
        expected_thread_columns = ['id', 'createdAt', 'updatedAt', 'name', 'tags', 'metadata', 'userId', 'userIdentifier']
        actual_thread_columns = [col['column_name'] for col in thread_schema]
        
        for col in thread_schema:
            status = "✅" if col['column_name'] in expected_thread_columns else "⚠️"
            print(f"  {status} {col['column_name']} ({col['data_type']}) - Default: {col['column_default']}")
        
        # 2. Check if there are any recent database connections/activity
        print("\n📊 CHECKING DATABASE ACTIVITY:")
        
        # Check if there are any threads created recently
        recent_threads = await conn.fetch("""
            SELECT id, name, "userIdentifier", "createdAt"
            FROM "Thread"
            ORDER BY "createdAt" DESC
            LIMIT 5;
        """)
        
        print(f"\n🧵 RECENT THREADS ({len(recent_threads)} found):")
        for thread in recent_threads:
            print(f"  - {thread['name']} (User: {thread['userIdentifier']}) - {thread['createdAt']}")
        
        # Check if there are any steps created recently
        recent_steps = await conn.fetch("""
            SELECT id, name, type, "threadId", "createdAt"
            FROM "Step"
            ORDER BY "createdAt" DESC
            LIMIT 5;
        """)
        
        print(f"\n📝 RECENT STEPS ({len(recent_steps)} found):")
        for step in recent_steps:
            print(f"  - {step['name']} ({step['type']}) - Thread: {step['threadId']} - {step['createdAt']}")
        
        # 3. Check database permissions and constraints
        print("\n🔒 CHECKING DATABASE CONSTRAINTS:")
        
        # Check foreign key constraints on Message table
        message_constraints = await conn.fetch("""
            SELECT conname, contype, confrelid::regclass as referenced_table
            FROM pg_constraint 
            WHERE conrelid = 'Message'::regclass;
        """)
        
        print(f"\n🔗 MESSAGE TABLE CONSTRAINTS:")
        for constraint in message_constraints:
            print(f"  - {constraint['conname']} ({constraint['contype']}) -> {constraint['referenced_table']}")
        
        # 4. Test manual message insertion with Chainlit schema
        print("\n🧪 TESTING MANUAL MESSAGE INSERTION:")
        
        try:
            # Create a test thread first
            test_thread_id = await conn.fetchval("""
                INSERT INTO "Thread" (name, "userIdentifier", "userId")
                VALUES ($1, $2, $3)
                RETURNING id;
            """, "Debug Test Thread", "debug_user", "debug_user_id")
            
            print(f"✅ Test thread created: {test_thread_id}")
            
            # Insert a test message
            test_message_id = await conn.fetchval("""
                INSERT INTO "Message" ("threadId", content, author, "authorIsUser")
                VALUES ($1, $2, $3, $4)
                RETURNING id;
            """, test_thread_id, "This is a test message from debug script", "debug_user", True)
            
            print(f"✅ Test message inserted: {test_message_id}")
            
            # Insert AI response
            ai_message_id = await conn.fetchval("""
                INSERT INTO "Message" ("threadId", content, author, "authorIsUser")
                VALUES ($1, $2, $3, $4)
                RETURNING id;
            """, test_thread_id, "This is an AI response from debug script", "AI Assistant", False)
            
            print(f"✅ AI message inserted: {ai_message_id}")
            
            # Verify messages were inserted
            message_count = await conn.fetchval('SELECT COUNT(*) FROM "Message";')
            print(f"✅ Message table now has {message_count} rows")
            
            # Clean up test data
            await conn.execute('DELETE FROM "Message" WHERE "threadId" = $1;', test_thread_id)
            await conn.execute('DELETE FROM "Thread" WHERE id = $1;', test_thread_id)
            print("✅ Test data cleaned up")
            
        except Exception as e:
            print(f"❌ Error testing message insertion: {e}")
        
        # 5. Check environment variables that might affect Chainlit
        print("\n🌍 CHECKING ENVIRONMENT CONFIGURATION:")
        
        env_vars = ['DATABASE_URL', 'CHAINLIT_AUTH_SECRET', 'OPENAI_API_KEY']
        for var in env_vars:
            value = os.getenv(var)
            if value:
                # Mask sensitive values
                if 'KEY' in var or 'SECRET' in var or 'PASSWORD' in var:
                    masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                    print(f"  ✅ {var}: {masked_value}")
                elif 'DATABASE_URL' in var:
                    # Show just the host part
                    if 'postgresql://' in value:
                        host_part = value.split('@')[1].split('/')[0] if '@' in value else "unknown"
                        print(f"  ✅ {var}: postgresql://***@{host_part}/***")
                    else:
                        print(f"  ✅ {var}: {value[:20]}...")
                else:
                    print(f"  ✅ {var}: {value}")
            else:
                print(f"  ❌ {var}: Not set")
        
        # 6. Recommendations
        print("\n💡 RECOMMENDATIONS:")
        print("1. Ensure Chainlit is configured to use PostgreSQL data layer")
        print("2. Check that DATABASE_URL environment variable is accessible to Chainlit")
        print("3. Verify that your app is creating threads and messages through Chainlit's API")
        print("4. Check Railway logs for any Chainlit data persistence errors")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error debugging persistence: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(debug_chainlit_persistence())
