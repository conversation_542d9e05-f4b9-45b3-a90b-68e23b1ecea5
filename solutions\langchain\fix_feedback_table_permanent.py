import os
import asyncio
import asyncpg
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def fix_feedback_table_permanent():
    """Fix the Feedback table by adding the missing 'name' column and setting up a trigger to maintain it."""
    # In Railway, we can use the internal connection string
    database_url = "postgresql://postgres:<EMAIL>:5432/railway"
    
    logging.info(f"Attempting to connect to database...")
    try:
        conn = await asyncpg.connect(database_url)
        logging.info("✅ Connected to database successfully")
    
        # Check if the Feedback table exists
        feedback_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Feedback'
            )
        """)
        
        if feedback_exists:
            logging.info("Feedback table exists, checking for 'name' column...")
            
            # Check if the 'name' column exists
            name_column_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'Feedback' AND column_name = 'name'
                )
            """)
            
            if name_column_exists:
                logging.info("✅ 'name' column already exists in Feedback table")
            else:
                logging.info("❌ 'name' column does not exist in Feedback table, adding it...")
                
                # Add the 'name' column to the Feedback table
                await conn.execute("""
                    ALTER TABLE "Feedback" 
                    ADD COLUMN name VARCHAR(255) DEFAULT 'feedback'
                """)
                
                logging.info("✅ Added 'name' column to Feedback table")
            
            # Create a function to ensure the name column exists
            logging.info("Creating function to ensure 'name' column exists...")
            await conn.execute("""
                CREATE OR REPLACE FUNCTION ensure_feedback_name_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    -- Check if the name column exists
                    IF NOT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_name = 'Feedback' AND column_name = 'name'
                    ) THEN
                        -- Add the name column
                        EXECUTE 'ALTER TABLE "Feedback" ADD COLUMN name VARCHAR(255) DEFAULT ''feedback''';
                    END IF;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            """)
            logging.info("✅ Created function to ensure 'name' column exists")
            
            # Create a trigger to run the function after table creation
            logging.info("Creating trigger to maintain 'name' column...")
            
            # First, check if the trigger already exists
            trigger_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM pg_trigger
                    WHERE tgname = 'ensure_feedback_name_column_trigger'
                )
            """)
            
            if trigger_exists:
                logging.info("Trigger already exists, dropping it first...")
                await conn.execute("""
                    DROP TRIGGER IF EXISTS ensure_feedback_name_column_trigger ON "Feedback";
                """)
            
            # Create the trigger
            await conn.execute("""
                CREATE TRIGGER ensure_feedback_name_column_trigger
                AFTER INSERT ON "Feedback"
                FOR EACH STATEMENT
                EXECUTE FUNCTION ensure_feedback_name_column();
            """)
            logging.info("✅ Created trigger to maintain 'name' column")
            
            # Show the current schema of the Feedback table
            columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'Feedback'
                ORDER BY ordinal_position
            """)
            
            logging.info("Current Feedback table schema:")
            for col in columns:
                logging.info(f"  - {col['column_name']} ({col['data_type']}, nullable: {col['is_nullable']})")
            
            # Get row count
            row_count = await conn.fetchval('SELECT COUNT(*) FROM "Feedback"')
            logging.info(f"Feedback table has {row_count} rows")
        else:
            logging.info("❌ Feedback table does not exist yet. It will be fixed when created.")
            
            # Create a function to ensure the name column exists when the table is created
            logging.info("Creating function to add 'name' column when Feedback table is created...")
            await conn.execute("""
                CREATE OR REPLACE FUNCTION ensure_feedback_table_has_name_column()
                RETURNS event_trigger AS $$
                BEGIN
                    -- Check if the Feedback table was just created
                    IF EXISTS (
                        SELECT 1 FROM pg_event_trigger_ddl_commands() 
                        WHERE object_identity = 'public.Feedback'
                    ) THEN
                        -- Check if the name column exists
                        IF NOT EXISTS (
                            SELECT FROM information_schema.columns 
                            WHERE table_name = 'Feedback' AND column_name = 'name'
                        ) THEN
                            -- Add the name column
                            EXECUTE 'ALTER TABLE "Feedback" ADD COLUMN name VARCHAR(255) DEFAULT ''feedback''';
                            RAISE NOTICE 'Added name column to Feedback table';
                        END IF;
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
            """)
            logging.info("✅ Created function to add 'name' column when Feedback table is created")
            
            # Create an event trigger to run the function after table creation
            logging.info("Creating event trigger...")
            
            # First, check if the event trigger already exists
            event_trigger_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM pg_event_trigger
                    WHERE evtname = 'ensure_feedback_table_has_name_column_trigger'
                )
            """)
            
            if event_trigger_exists:
                logging.info("Event trigger already exists, dropping it first...")
                await conn.execute("""
                    DROP EVENT TRIGGER IF EXISTS ensure_feedback_table_has_name_column_trigger;
                """)
            
            # Create the event trigger
            await conn.execute("""
                CREATE EVENT TRIGGER ensure_feedback_table_has_name_column_trigger
                ON ddl_command_end
                WHEN tag IN ('CREATE TABLE')
                EXECUTE FUNCTION ensure_feedback_table_has_name_column();
            """)
            logging.info("✅ Created event trigger to add 'name' column when Feedback table is created")
        
        # Close the connection
        await conn.close()
        logging.info("Database connection closed")
        return True
        
    except Exception as e:
        logging.error(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(fix_feedback_table_permanent())
