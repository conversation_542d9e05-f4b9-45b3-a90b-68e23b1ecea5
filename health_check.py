import http.server
import socketserver
import os
import sys

class HealthCheckHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        print(f"Received request for path: {self.path}")
        sys.stdout.flush()

        # Always return 200 OK for any path
        self.send_response(200)
        self.send_header("Content-type", "text/plain")
        self.end_headers()
        self.wfile.write("Health check OK".encode())
        print("Sent health check response")
        sys.stdout.flush()

def run_health_server():
    # Use a different port for the health check server
    health_port = int(os.environ.get("HEALTH_PORT", 8080))
    print(f"Starting health check server on port {health_port}")
    sys.stdout.flush()

    with socketserver.TCPServer(("0.0.0.0", health_port), HealthCheckHandler) as httpd:
        print(f"Health check server started at port {health_port}")
        sys.stdout.flush()
        httpd.serve_forever()

if __name__ == "__main__":
    run_health_server()
