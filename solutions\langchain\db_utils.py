import os
import logging
import asyncpg
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def save_session_to_db(student_id, current_step_index, total_interactions=None, last_message_content=None):
    """Save the current session state to the database."""
    try:
        # Get database URL - try Railway internal URL first, then fall back to environment variable
        database_url = "postgresql://postgres:<EMAIL>:5432/railway"

        try:
            # Try connecting with Railway internal URL first
            conn = await asyncpg.connect(database_url)
            logging.info("Connected to database using Railway internal URL")
        except Exception as db_error:
            # If that fails, try the environment variable
            logging.warning(f"Failed to connect with Railway internal URL: {db_error}")
            database_url = os.getenv("DATABASE_URL")
            if not database_url:
                logging.warning("DATABASE_URL environment variable not set")
                return False
            conn = await asyncpg.connect(database_url)
            logging.info("Connected to database using DATABASE_URL environment variable")

        # Insert or update the session data
        # Use NOW() from PostgreSQL instead of Python datetime to avoid timezone issues
        update_fields = ["current_step_index = $2", "last_updated = NOW()"]
        params = [student_id, current_step_index]
        
        # Add total_interactions if provided
        if total_interactions is not None:
            update_fields.append("total_interactions = $3")
            params.append(total_interactions)
            
        # Add last_message_content if provided
        if last_message_content is not None:
            update_fields.append("last_message_content = $" + str(len(params) + 1))
            params.append(last_message_content)
            
        # Build the SQL query
        query = f"""
            INSERT INTO student_sessions (student_id, current_step_index, last_updated)
            VALUES ($1, $2, NOW())
            ON CONFLICT (student_id)
            DO UPDATE SET
                {", ".join(update_fields)}
        """
        
        await conn.execute(query, *params)

        await conn.close()
        logging.info(f"Session saved for student {student_id}")
        return True
    except Exception as e:
        logging.error(f"Error saving session: {e}")
        return False

async def save_pitch_evaluation(student_id, step_name, score, feedback):
    """Save a pitch evaluation to the database."""
    try:
        # Get database URL - try Railway internal URL first, then fall back to environment variable
        database_url = "postgresql://postgres:<EMAIL>:5432/railway"

        try:
            # Try connecting with Railway internal URL first
            conn = await asyncpg.connect(database_url)
            logging.info("Connected to database using Railway internal URL")
        except Exception as db_error:
            # If that fails, try the environment variable
            logging.warning(f"Failed to connect with Railway internal URL: {db_error}")
            database_url = os.getenv("DATABASE_URL")
            if not database_url:
                logging.warning("DATABASE_URL environment variable not set")
                return False
            conn = await asyncpg.connect(database_url)
            logging.info("Connected to database using DATABASE_URL environment variable")

        # Insert the evaluation data
        await conn.execute("""
            INSERT INTO pitch_evaluations (student_id, step_name, score, feedback, evaluation_date)
            VALUES ($1, $2, $3, $4, NOW())
        """, student_id, step_name, score, feedback)

        # Update the student's session to increment completed_steps
        await conn.execute("""
            UPDATE student_sessions
            SET completed_steps = COALESCE(completed_steps, 0) + 1
            WHERE student_id = $1
        """, student_id)

        await conn.close()
        logging.info(f"Pitch evaluation saved for student {student_id}, step {step_name}")
        return True
    except Exception as e:
        logging.error(f"Error saving pitch evaluation: {e}")
        return False

async def increment_interactions(student_id):
    """Increment the total_interactions counter for a student."""
    try:
        # Get database URL - try Railway internal URL first, then fall back to environment variable
        database_url = "postgresql://postgres:<EMAIL>:5432/railway"

        try:
            # Try connecting with Railway internal URL first
            conn = await asyncpg.connect(database_url)
            logging.info("Connected to database using Railway internal URL")
        except Exception as db_error:
            # If that fails, try the environment variable
            logging.warning(f"Failed to connect with Railway internal URL: {db_error}")
            database_url = os.getenv("DATABASE_URL")
            if not database_url:
                logging.warning("DATABASE_URL environment variable not set")
                return False
            conn = await asyncpg.connect(database_url)
            logging.info("Connected to database using DATABASE_URL environment variable")

        # Update the student's session to increment total_interactions
        await conn.execute("""
            UPDATE student_sessions
            SET total_interactions = COALESCE(total_interactions, 0) + 1
            WHERE student_id = $1
        """, student_id)

        await conn.close()
        logging.info(f"Incremented interactions for student {student_id}")
        return True
    except Exception as e:
        logging.error(f"Error incrementing interactions: {e}")
        return False
