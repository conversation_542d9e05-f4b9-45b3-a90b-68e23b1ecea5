import chainlit as cl
import json
import uuid
from datetime import datetime
import logging
import psycopg2
from psycopg2 import pool
import asyncpg
from docx import Document
import PyPDF2
from langchain_core.callbacks import BaseCallbackHandler
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain.callbacks.manager import CallbackManager
import os
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from chainlit import AskFileMessage
import re

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# --- PostgreSQL Utility Functions ---
# USE_DATABASE = True  # Toggle DB integration - REMOVE THIS

def get_db_connection():
    """Establish database connection"""
    try:
        return psycopg2.connect(os.getenv("DATABASE_URL"))
    except Exception as e:
        logging.error(f"❌ Database connection failed: {e}")
        return None

def save_evaluation_to_db(student_id, pitch_text, evaluation_score, feedback):
    """Save pitch evaluation to database"""
    conn = get_db_connection()
    if not conn:
        return

    try:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO pitch_evaluations 
                (student_id, pitch_text, evaluation_score, feedback)
                VALUES (%s, %s, %s, %s)
            """, (student_id, pitch_text, evaluation_score, feedback))
            conn.commit()
            logging.info(f"✅ Evaluation saved to database for student {student_id}")
    except Exception as e:
        logging.error(f"❌ Error saving evaluation: {e}")
    finally:
        conn.close()

api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

llm = ChatOpenAI(
    model="gpt-4o",  
    temperature=0.7,
    openai_api_key=api_key  
)

class StreamHandler(BaseCallbackHandler):
    def __init__(self, message_id: str):
        self.message_id = message_id
        self.content = ""

    async def on_llm_new_token(self, token: str, **kwargs):
        self.content += token
        await cl.Message(id=self.message_id, content=self.content).update()

    async def on_llm_end(self, response, **kwargs):
        pass

    async def on_llm_error(self, error, **kwargs):
        await cl.Message(id=self.message_id, content=f"Error: {error}").update()

# --- Elevator Pitch Steps ---
pitch_steps = [
    "Identify the Target Audience",
    "Define the Problem/Need",
    "Introduce the Product/Service",
    "Highlight the Key Differentiator",
    "End with a Strong Closing Statement"
]

# --- Define AI Agents ---
PROMPTS = {
    "mentor": PromptTemplate(
        input_variables=["context", "question", "current_step"],
        template="""
        [SYSTEM MESSAGE]
        You are the Mentor Agent, guiding students through creating an elevator pitch.
        🚀 **Current Step:** {current_step}
        - NEVER skip a step in the process.
        - Ask questions that ensure students fully understand each step before moving forward.
        - Challenge their responses and encourage reflection.
        - If they try to skip ahead, redirect them to the current step.

        **Current Progress:**
        {context}

        **Student Question:**
        {question}

        **Mentor's Guided Response:**
        """
    ),
    "peer": PromptTemplate(
        input_variables=["context", "question"],
        template="""
        [SYSTEM MESSAGE]
        You are the Peer Agent, just a regular student helping another student brainstorm an elevator pitch. 
    - Keep the conversation chill, friendly, and informal.
    - You are NOT an expert—don’t give structured frameworks or detailed feedback.
    - Instead, react naturally and ask simple, curious questions.
    - If the user asks "Who are you?", respond: "Hey! I'm your Peer Agent, just a fellow student here to bounce ideas with you. No pressure, let’s figure this out together!"

        **Conversation History:**
        {context}

        **Student Message:**
        {question}

        **Peer’s Thoughtful Response:**
        """
    ),
    "progress": PromptTemplate(
        input_variables=["context", "question", "student_progress", "time_spent"],
        template="""
        [SYSTEM MESSAGE]
        You are the Progress Agent, tracking the student’s progress through their elevator pitch creation.
        - Track student step progress and time spent.
        - Retrieve stored progress from the database.

        ✅ **Current Step:** {student_progress}
        ⏳ **Time Spent on This Step:** {time_spent} seconds

        **Conversation History:**
        {context}

        **Student Message:**
        {question}

        **Progress Tracking Response:**
        """
    ),
    "eval": PromptTemplate(
        input_variables=["pitch"],
        template="""
        [SYSTEM MESSAGE]
        You are the Evaluator Agent, responsible for reviewing and grading elevator pitches.
        - Score each pitch out of 10 based on: 
          1️⃣ **Clarity**  
          2️⃣ **Engagement**  
          3️⃣ **Persuasiveness**  
          4️⃣ **Structure**  
          5️⃣ **Effectiveness**  

        - Provide structured feedback on strengths & areas for improvement.

        **Student Pitch:**
        {pitch}

        **Evaluation Feedback & Score:**
        """
    )
}

# --- Memory Management ---
memory = {agent: ChatMessageHistory() for agent in PROMPTS.keys()}

# --- Message Utility ---
from langchain_core.messages.base import BaseMessage

def flatten_messages(messages):
    """Safely converts list of messages (AIMessage, HumanMessage) to a formatted string."""
    if isinstance(messages, list) and all(isinstance(m, BaseMessage) for m in messages):
        return "\n".join([f"{m.type}: {m.content}" for m in messages])
    return str(messages)

# --- Create LLM Chains for Agents ---
agents = {agent: PROMPTS[agent] | llm for agent in PROMPTS.keys()}

# --- Function to Extract Text from File ---
def extract_text_from_file(file: cl.File):
    if file.name.endswith(".pdf"):
        pdf_reader = PyPDF2.PdfReader(file.path)
        text = "\n".join([page.extract_text() for page in pdf_reader.pages if page.extract_text()])
    elif file.name.endswith(".docx"):
        doc = Document(file.path)
        text = "\n".join([para.text for para in doc.paragraphs])
    else:
        return None
    return text

@cl.on_chat_start
async def start():
    # Create new thread - REMOVE THIS
    # thread_id = create_thread()
    # cl.user_session.set("thread_id", thread_id)

    cl.user_session.set("agent", "mentor")
    cl.user_session.set("student_id", f"student_{uuid.uuid4().hex[:8]}")  # Generate temporary student ID
    
    welcome_message = """
    🎓 **Welcome to the AI Elevator Pitch Tutor!** 🏆

    🤖 This tutor is powered by **four AI agents**:
    - 🧑‍🏫 **Mentor Agent** → Guides you step-by-step.
    - 👥 **Peer Agent** → Thinks with you like a fellow student.
    - 📈 **Progress Agent** → Tracks your step-by-step progress.
    - 📝 **Evaluator Agent** → Reviews and scores your final pitch.

    💡 **Type `/upload` when you're ready to submit your pitch.**
    You can also use the 📎 button to attach a `.pdf` or `.docx`.

    🔄 **Switch Agents Anytime**:
    - `/mentor`, `/peer`, `/progress`, `/eval`

    🔹 You're now talking to the **Mentor Agent**.
    """
    await cl.Message(content=welcome_message).send()

@cl.on_message
async def main(message: cl.Message):
    selected_agent = cl.user_session.get("agent")
    # thread_id = cl.user_session.get("thread_id") # REMOVE THIS
    student_id = cl.user_session.get("student_id")

    # Save user message
    # save_message(thread_id, message.content, "user") # REMOVE THIS
    # save_chat_history(student_id, selected_agent, "user", message.content) # REMOVE THIS

    if message.content.lower() == "/upload":
        files = await cl.AskFileMessage(
            content="Please upload your pitch document (PDF or DOCX)",
            accept=["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
            max_size_mb=20,
            timeout=180,
        ).send()
        
        if files:
            await process_file(files[0])
        return

    if message.content.lower() in ["/mentor", "/peer", "/progress", "/eval"]:
        new_agent = message.content.lower()[1:]
        cl.user_session.set("agent", new_agent)
        response = f"🔹 Switched to **{new_agent.capitalize()} Agent**"
        await cl.Message(content=response).send()
        
        # Save agent switch in database
        # save_step(thread_id, "agent_switch", "system", message.content, response) # REMOVE THIS
        return

    try:
        # Create a message placeholder
        msg = cl.Message(content="")
        await msg.send()

        # Set up the streaming callback
        callback = StreamHandler(msg.id)
        llm_with_callback = llm.bind(
            callbacks=[callback],
            streaming=True
        )

        # Create chain with memory
        chain = agents[selected_agent] | llm_with_callback

        # Execute the chain and get response
        chain_input = {
            "context": "\n".join([
                f"{m.type}: {m.content}" 
                for m in memory[selected_agent].messages
            ]) if memory[selected_agent].messages else "",
            "question": message.content,
            "current_step": cl.user_session.get("current_step", "initial"),
            "student_progress": cl.user_session.get("student_progress", "Not started"),
            "time_spent": cl.user_session.get("time_spent", 0)
        }
        
        # Use the existing flatten_messages function
        response = await chain.ainvoke({
            **chain_input,
            "context": flatten_messages(memory[selected_agent].messages)
        })
        
        # Extract the text content
        ai_text = response if isinstance(response, str) else response.content if hasattr(response, 'content') else str(response)
        
        # Update the message
        await msg.update(content=ai_text)
        
        # Save to memory and database
        memory[selected_agent].add_message(AIMessage(content=ai_text))

        try:
            # save_message(thread_id, ai_text, "assistant") # REMOVE THIS
            # save_chat_history(student_id, selected_agent, "assistant", ai_text) # REMOVE THIS
            # save_step( # REMOVE THIS
            #     thread_id,
            #     cl.user_session.get("current_step", "initial"),
            #     f"{selected_agent}_interaction",
            #     message.content,
            #     ai_text
            # )
            async with cl.Step(name=cl.user_session.get("current_step", "initial"), type=f"{selected_agent}_interaction", input=message.content, output=ai_text) as step:
                pass # Chainlit automatically saves the step

        except Exception as db_error:
            logging.error(f"Database operation failed: {db_error}")
            # Continue execution even if database operations fail
            
    except Exception as e:
        logging.error(f"❌ Error in message processing: {e}")
        await cl.Message(content=f"An error occurred: {e}").send()

async def process_file(file: cl.File):
    pitch_text = extract_text_from_file(file)
    student_id = cl.user_session.get("student_id", "student_123")  # Add real ID logic later

    if not pitch_text:
        await cl.Message(content="❌ Couldn't extract text. Please upload a valid `.pdf` or `.docx`.").send()
        return

    try:
        response = await agents["eval"].ainvoke(
            {"pitch": pitch_text},
            config={"callbacks": [cl.AsyncLangchainCallbackHandler()]}
        )

        feedback = response.content if isinstance(response, AIMessage) else str(response)

        # Try to extract a score from the feedback (e.g., "Score: 8/10")
        match = re.search(r"[Ss]core[:\s]+(\d{1,2})\s*/\s*10", feedback)
        score = int(match.group(1)) if match else None

        # Save to database
        save_evaluation_to_db(student_id, pitch_text, score, feedback)

        # Show feedback in chat
        await cl.Message(content=f"✅ Evaluation Complete:\n\n{feedback}").send()

    except Exception as e:
        logging.error(f"❌ Evaluation Error: {e}")
        await cl.Message(content="⚠️ Error during pitch evaluation. Please try again.").send()

@cl.on_stop
async def stop_execution():
    cl.user_session.clear()
    await cl.Message("Session stopped. Your progress is saved!").send()


