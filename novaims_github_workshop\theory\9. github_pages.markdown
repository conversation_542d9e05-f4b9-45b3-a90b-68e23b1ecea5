# GitHub Pages: Your Code, Live on the Web 🌐✨

## What is GitHub Pages?

You’ve built something cool—maybe a portfolio, a blog, a data dashboard, or even just a pretty README and now you want to share it with the world.  
**GitHub Pages** are an easy way to turn your GitHub repo into a live website. For free. No servers, no nonsense. Just push your code and it’s online. 🎉
And you have up to 4 of these pages for free!

## So... How Does It Work?

GitHub Pages takes files from your repo (typically HTML, CSS, and JS), and serves them up as a website.  
It’s perfect for hosting:

- Static websites (HTML/CSS/JS)
- Jupyter notebooks (via nbviewer-style render)
- Markdown files rendered as simple pages
- Even React or Vue apps with the right build step!

## Setup: It's Easier Than You Think

Let’s say you have a folder with a simple site—`index.html`, maybe some CSS or JS—and you want it live. Here's the quick way:

1. Push your files to a repo.
2. Go to **Settings → Pages** in your GitHub repo.
3. Under **"Source"**, choose the main folder for your project you want to host.
4. GitHub gives you a public URL like:  
   `https://your-username.github.io/your-repo-name`

And you’re published!

