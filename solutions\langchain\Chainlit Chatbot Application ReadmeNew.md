# Chainlit Chatbot Application

This application demonstrates a chatbot built using <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and Ollama. It provides a user interface for interacting with a locally-run large language model (LLM).

## Prerequisites

Before running this application, ensure you have the following installed:

1.  **Python 3.9+:**  This application is built using Python. Make sure you have Python 3.9 or a later version installed. You can check with: `python --version` or `python3 --version`
2.  **Ollama:** You need to have Ollama installed on your system.
    *   **Installation:** Follow the instructions on the [Ollama website](https://ollama.com/) for your operating system (Windows, macOS, or Linux).
    *   **Model:** Make sure you have at least one compatible model downloaded. See the **Downloading Models** section below.
3.  **Git:** Git is necessary to clone the repository. You can download it from [Git](https://git-scm.com/downloads).

## Installation

1.  **Clone the Repository:**
    ```bash
    git clone <your_repository_url>
    cd <your_repository_name> #Replace <your_repository_name>
    ```
    *   Replace `<your_repository_url>` with the actual URL of your repository.
    *   Replace `<your_repository_name>` with the repository name.

2.  **Create a Virtual Environment (Recommended):**
    ```bash
    python -m venv .venv
    ```
    *   This creates a virtual environment in a folder named `.venv`.
    * Using a virtual environment helps isolate the dependencies for this project.

3.  **Activate the Virtual Environment:**
    *   **Windows (Command Prompt):**
        ```bash
        .venv\Scripts\activate
        ```
    *   **Windows (PowerShell):**
        ```bash
        .venv\Scripts\Activate.ps1
        ```
    *   **macOS/Linux:**
        ```bash
        source .venv/bin/activate
        ```

4.  **Install Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
    *   This installs the required Python packages listed in the `requirements.txt` file.
    *   If you don't have the `requirements.txt` file, run: `pip install chainlit langchain-community langchain`

## Downloading Models

Before you can run the application, you need to download an LLM model using Ollama:

1.  **Open a new terminal** (separate from the one where you'll run the app).
2.  **Pull the Model:**
    ```bash
    ollama pull tinyllama:1.1b-chat
    ```
    *   This command will download the `tinyllama:1.1b-chat` model.
    *   You can replace `tinyllama:1.1b-chat` with other models, like `llama2:7b-chat-q2_K`.
3. **List your models:**
    ```bash
    ollama list
    ```
    * This will allow you to see the models that you have downloaded.

## Running the Application

### Cleaning Up Existing Ollama Processes (Important!)

If you've run Ollama before, it's crucial to ensure that no old processes are still running. This can cause errors.

1.  **Identify Existing Ollama Processes:**
    *   Open a **new** terminal or command prompt **as Administrator** (right-click and choose "Run as administrator").
    *   Run the following command:
        ```bash
        netstat -ano | findstr :11434
        ```
        *   This command lists any processes using port 11434 (Ollama's default port).
2.  **Kill Existing Processes:**
    *   If the `netstat` command lists any processes, you need to kill them.
    *   In the **same administrative terminal**, run:
        ```bash
        taskkill /F /PID <process_id>
        ```
        *   Replace `<process_id>` with the actual process ID from the `netstat` output.
        *   Repeat this command for each process using port 11434.

### Starting the Ollama Server

1.  **Open a *new* terminal** (separate from your admin terminal).
2.  **Start Ollama:**
    ```bash
    ollama serve
    ```
    *   This starts the Ollama server in the background. You will see a message like this `2025-03-04 14:41:50.211885420 +0000 UTC m=server starting app=ollama`. Keep this terminal running.

### Starting the Chainlit App

1.  **Open *another* new terminal** (you should have three terminals open now: admin, Ollama, and Chainlit).
2.  **Activate your virtual environment (if you created one):**
      *   **Windows (Command Prompt):**
        ```bash
        .venv\Scripts\activate
        ```
      *   **Windows (PowerShell):**
        ```bash
        .venv\Scripts\Activate.ps1
        ```
      *   **macOS/Linux:**
        ```bash
        source .venv/bin/activate
        ```
3.  **Navigate to the Project Directory:**
    ```bash
    cd <your_repository_name>\solutions\langchain
    ```
4.  **Run the Chainlit App:**
    ```bash
    chainlit run my_agent_bot.py -w
    ```
    *   This command starts the Chainlit application.

### Accessing the Chatbot

1.  Once the Chainlit app is running, you will see a message similar to: `2025-03-04 14:45:58 - Your app is available at http://localhost:8000`
2.  Open your web browser and go to `http://localhost:8000`. You should see the chatbot interface.

## Troubleshooting

*   **"Ollama call failed with status code 500" or "Could not reach the server":** This usually means the Ollama server isn't running, or something is blocking the connection.
    *   Make sure you ran `ollama serve` *before* running the Chainlit app.
    *   Ensure you've cleaned up old Ollama processes (see the **Cleaning Up Existing Ollama Processes** section).
    *   Check your firewall/antivirus settings to make sure they're not blocking the connection to `localhost:11434`.
* **Error using the port 11434:** if you get `Error: listen tcp 127.0.0.1:11434: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.`, you may have not cleaned the ollama processes correctly. Make sure to do it as administrator.
*   **Model Not Found:** If you get an error that the model can't be found, make sure the model name in `my_agent_bot.py` (e.g., `tinyllama:1.1b-chat`) *exactly* matches the name in `ollama list`.
* **Chainlit error:** Check your terminals to see if any error is being displayed.
* **Ollama error:** Check the log file at `%USERPROFILE%\.ollama\logs\server.log` to see if any error has been raised.

## Additional Notes

*   **Model Choice:** You can modify the `model` parameter in `my_agent_bot.py` to use different models (e.g., `llama2:7b-chat-q2_K`) but you need to pull that model first.
*   **Performance:** Keep in mind that the performance of the chatbot depends on the model and your computer's resources. Larger models (e.g. `deepseek-r1:7b`) will require more RAM and processing power.
* **Virtual environment:** Using a virtual environment is highly recommended.
* **Administrator:** Killing the ollama processes should always be done as administrator.

---

