# Git Concepts

## What is Git?

So, you might have heard the term Git thrown around or else you are wondering why are you learning this on the first place, so what is it? 🤔 

- Git is _version control system_. Huh? Yes, fancy words... 

Think of it like a time machine 🕰️ for your code. 


- It allows you to snap 'pictures' of your code. 
- _you can hop back and forth between these snapshots anytime you want!_.

Spilled coffee on your keyboard and accidentally deleted half your code? No worries! Git's got your back. 🙌

But wait, there's more! 
- It can spot the differences between these snapshots.

![Git](images/git3.png)


So imagine a team of coders, each working from the same starting point, tweaking different parts of the code. 
Git helps weave these different changes together, ensuring they fit perfectly. 
Best of all? 
You control this process the entire time, deciding what to keep, what to delete or even how to combine these changes.

And if you're wondering why Git and not any other similar system, the reasons are: 
- it's fast; 
- it's secure; 
- it's flexible, 
- it's free! 

The only downside is that it can be a bit hard to learn and that is why we are here. 


## If that is Git, then what is GitHub?

Think of GitHub as a meeting point for coders, offering a space to store and share those code 'snapshots' we talked about.

 **This repo is on Github, of course!**

One of its standout features? 
- Collaboration tools. You can suggest changes, review code, and track issues;
- Makes working together smoother and more transparent;
- Allows for managing user permissions;
- Github automation tools (which we will talk later)

<img src="images/git.png" alt="GitHub" width="300"/>


All in all, GitHub is more than just a storage space; it's a comprehensive toolkit for modern developers. 

In case you are looking for something more inspirational, let Github tell you what Github is.

[Link to video](https://www.youtube.com/watch?v=pBy1zgt0XPc)


## VSCode

In this Workshop, we will teach you how to run Git from the terminal. 
Even though working in the terminal is more than enough, VSCode has Git and Github integrations that will make your life easier.

Not only does it come with some basic functionalities by default, but there are several VSCode extensions that will take your Git capabilities to another level.

![alt text](images/gitvscode.png)
