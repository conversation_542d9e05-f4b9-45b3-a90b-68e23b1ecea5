# 🚀 Ngrok Backup Deployment Guide for AI Elevator Pitch Tutor

## 📋 Overview
This guide provides a comprehensive backup deployment method using ngrok for your AI Elevator Pitch Tutor application. This ensures your June 2nd, 2025 experiment can proceed even if Railway experiences downtime or account issues.

## ⚠️ CRITICAL IMPORTANCE
- **Primary Backup**: Use if <PERSON> is unavailable on experiment day
- **Quick Setup**: Can be deployed in under 5 minutes
- **Full Functionality**: Maintains all features including database connectivity
- **Experiment Continuity**: Ensures your thesis experiment proceeds as planned

---

## 🛠️ Prerequisites

### System Requirements
- ✅ Windows 10/11
- ✅ Python 3.8+ with Chainlit installed
- ✅ Ngrok installed (you already have this)
- ✅ Railway PostgreSQL database access
- ✅ OpenAI API key configured

### Environment Setup Verification
```bash
# Verify Python and Chainlit
python --version
chainlit --version

# Verify ngrok installation
ngrok version

# Check environment variables
echo $OPENAI_API_KEY
echo $DATABASE_URL
```

---

## 🔧 Step 1: Ngrok Installation (Already Completed)

Since you already have ngrok installed, verify your setup:

### 1.1 Check <PERSON>rok Status
```bash
ngrok version
```

### 1.2 Verify Authentication
```bash
ngrok config check
```

### 1.3 Test Basic Functionality
```bash
ngrok http 8000 --log=stdout
```
*Press Ctrl+C to stop the test*

---

## 🚀 Step 2: Local Application Setup

### 2.1 Navigate to Project Directory
```bash
cd "C:\Users\<USER>\Documents\Masters Project"
```

### 2.2 Activate Virtual Environment
```bash
# If using conda
conda activate chainlit_2

# If using venv
# .venv\Scripts\activate
```

### 2.3 Verify Environment Variables
Create a quick verification script:
```bash
python -c "
import os
print('OPENAI_API_KEY:', 'SET' if os.getenv('OPENAI_API_KEY') else 'MISSING')
print('DATABASE_URL:', 'SET' if os.getenv('DATABASE_URL') else 'MISSING')
"
```

---

## 🌐 Step 3: Starting the Chainlit Application

### 3.1 Standard Startup Command
```bash
chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0
```

### 3.2 Alternative Startup (if path issues)
```bash
cd solutions/langchain
chainlit run my_agent_bot.py --port 8000 --host 0.0.0.0
```

### 3.3 Verify Local Access
Open browser and navigate to:
```
http://localhost:8000
```

### 3.4 Expected Output
```
2025-06-02 08:00:00 - Your app is available at http://localhost:8000
2025-06-02 08:00:00 - Connected to database using Railway internal URL
```

---

## 🔗 Step 4: Creating Ngrok Tunnel

### 4.1 Basic Tunnel Command
```bash
ngrok http 8000
```

### 4.2 Enhanced Tunnel with Custom Subdomain (if available)
```bash
ngrok http 8000 --subdomain=ai-tutor-experiment
```

### 4.3 Tunnel with Authentication (Recommended for Experiment)
```bash
ngrok http 8000 --basic-auth="experiment:june2025"
```

### 4.4 Production-Ready Tunnel Command
```bash
ngrok http 8000 --log=stdout --log-level=info --region=us
```

---

## 📱 Step 5: Obtaining and Sharing Public URL

### 5.1 Reading Ngrok Output
After running ngrok, you'll see output like:
```
ngrok by @inconshreveable

Session Status                online
Account                       <EMAIL>
Version                       3.x.x
Region                        United States (us)
Latency                       45ms
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:8000

Connections                   ttl     opn     rt1     rt5     p50     p90
                              0       0       0.00    0.00    0.00    0.00
```

### 5.2 Extract Public URL
The public URL is: `https://abc123.ngrok.io`

### 5.3 Share with Experiment Participants
**For June 2nd Experiment:**
- **URL**: `https://your-ngrok-url.ngrok.io`
- **Instructions**: "Click the link and begin your elevator pitch session"
- **Backup Contact**: Provide your contact for technical issues

### 5.4 URL Management
```bash
# Get current tunnel info
curl http://localhost:4040/api/tunnels

# Monitor tunnel status
ngrok status
```

---

## 🔍 Step 6: Testing the Complete Setup

### 6.1 Basic Connectivity Test
```bash
curl https://your-ngrok-url.ngrok.io/health
```

### 6.2 Full Application Test
1. Open `https://your-ngrok-url.ngrok.io` in browser
2. Verify AI Tutor loads correctly
3. Test student ID generation
4. Send a test message
5. Verify database connectivity

### 6.3 Database Connectivity Verification
```bash
python -c "
import asyncio
import asyncpg
import os

async def test_db():
    try:
        conn = await asyncpg.connect(os.getenv('DATABASE_URL'))
        count = await conn.fetchval('SELECT COUNT(*) FROM \"Thread\";')
        print(f'✅ Database connected. Threads: {count}')
        await conn.close()
    except Exception as e:
        print(f'❌ Database error: {e}')

asyncio.run(test_db())
"
```

---

## 🛡️ Step 7: Security Considerations

### 7.1 Basic Authentication (Recommended)
```bash
ngrok http 8000 --basic-auth="username:password"
```

### 7.2 IP Whitelisting (if needed)
```bash
ngrok http 8000 --cidr-allow="***********/24"
```

### 7.3 HTTPS Enforcement
Ngrok automatically provides HTTPS. Always share the `https://` URL, not `http://`.

### 7.4 Session Security
- Ngrok URLs are temporary and change on restart
- No persistent data stored locally
- Database remains on Railway (secure)

---

## ⏱️ Step 8: Session Management for Experiment

### 8.1 Pre-Experiment Setup (30 minutes before)
```bash
# 1. Start Chainlit application
chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0

# 2. In new terminal, start ngrok
ngrok http 8000 --log=stdout

# 3. Test the public URL
# 4. Share URL with participants
```

### 8.2 During Experiment Monitoring
```bash
# Monitor ngrok web interface
http://localhost:4040

# Check application logs in Chainlit terminal
# Monitor database activity if needed
```

### 8.3 Session Persistence
- **Student Sessions**: Stored in Railway database (persistent)
- **Ngrok Tunnel**: Remains active as long as process runs
- **Application State**: Maintained in database, not local memory

---

## 🚨 Step 9: Troubleshooting Common Issues

### 9.1 Ngrok Connection Issues

**Problem**: "Failed to start tunnel"
```bash
# Solution 1: Check ngrok authentication
ngrok config check

# Solution 2: Try different port
ngrok http 8001

# Solution 3: Restart ngrok service
taskkill /f /im ngrok.exe
ngrok http 8000
```

**Problem**: "Tunnel not found"
```bash
# Check active tunnels
curl http://localhost:4040/api/tunnels

# Restart with verbose logging
ngrok http 8000 --log=stdout --log-level=debug
```

### 9.2 Chainlit Application Issues

**Problem**: "Address already in use"
```bash
# Find process using port 8000
netstat -ano | findstr :8000

# Kill the process (replace PID)
taskkill /PID <process_id> /F

# Restart Chainlit
chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0
```

**Problem**: "Module not found"
```bash
# Verify you're in correct directory
pwd

# Activate virtual environment
conda activate chainlit_2

# Install missing dependencies
pip install -r requirements.txt
```

### 9.3 Database Connectivity Issues

**Problem**: "Database connection failed"
```bash
# Check DATABASE_URL environment variable
echo $DATABASE_URL

# Test direct connection
python -c "
import os
import asyncpg
import asyncio

async def test():
    try:
        conn = await asyncpg.connect(os.getenv('DATABASE_URL'))
        print('✅ Database connection successful')
        await conn.close()
    except Exception as e:
        print(f'❌ Database connection failed: {e}')

asyncio.run(test())
"
```

### 9.4 Performance Issues

**Problem**: "Slow response times"
```bash
# Use regional ngrok server
ngrok http 8000 --region=us

# Monitor bandwidth usage
# Check ngrok web interface at http://localhost:4040
```

**Problem**: "Timeout errors"
```bash
# Increase timeout settings
ngrok http 8000 --timeout=30s
```

---

## 🌍 Step 10: Database Connectivity Considerations

### 10.1 Local vs Railway Database Access

**Railway Database URL (External)**:
```
postgresql://postgres:<EMAIL>:20482/railway
```

**Connection Priority**:
1. ✅ External Railway URL (works from anywhere)
2. ❌ Internal Railway URL (only works within Railway)

### 10.2 Environment Variable Setup
```bash
# Set DATABASE_URL for local development
set DATABASE_URL=postgresql://postgres:<EMAIL>:20482/railway

# Verify it's set
echo %DATABASE_URL%
```

### 10.3 Database Performance Considerations
- **Latency**: External connection adds ~50-100ms
- **Reliability**: Railway database remains highly available
- **Data Consistency**: All data stored in same Railway database
- **Backup**: Railway provides automatic backups

---

## 📊 Step 11: Monitoring and Logging

### 11.1 Ngrok Web Interface
Access: `http://localhost:4040`

**Features**:
- Real-time request monitoring
- Response time tracking
- Error rate monitoring
- Request/response inspection

### 11.2 Application Logging
```bash
# Enhanced logging for Chainlit
chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0 --debug

# Save logs to file
chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0 > experiment_logs.txt 2>&1
```

### 11.3 Database Monitoring
```bash
# Quick database status check
python -c "
import asyncio
import asyncpg
import os

async def check_status():
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'))

    tables = ['Element', 'Message', 'Feedback', 'Thread', 'student_sessions', 'pitch_evaluations']
    for table in tables:
        count = await conn.fetchval(f'SELECT COUNT(*) FROM \"{table}\";')
        print(f'{table}: {count} rows')

    await conn.close()

asyncio.run(check_status())
"
```

---

## 🎯 Step 12: Experiment Day Protocol

### 12.1 Pre-Experiment Checklist (1 hour before)
- [ ] Clean database tables
- [ ] Test local Chainlit application
- [ ] Start ngrok tunnel
- [ ] Test public URL functionality
- [ ] Verify student ID generation
- [ ] Test complete interaction flow
- [ ] Share URL with participants
- [ ] Prepare backup contact method

### 12.2 During Experiment
- [ ] Monitor ngrok web interface
- [ ] Watch Chainlit application logs
- [ ] Check database activity periodically
- [ ] Be ready to restart services if needed
- [ ] Have Railway backup ready

### 12.3 Emergency Procedures
**If ngrok fails**:
1. Restart ngrok with new URL
2. Immediately notify participants of new URL
3. Switch to Railway if available

**If local application fails**:
1. Restart Chainlit application
2. Check error logs
3. Switch to Railway deployment

**If database fails**:
1. Check Railway database status
2. Verify DATABASE_URL environment variable
3. Test database connection manually

---

## 📞 Emergency Contact Information

### Quick Reference Commands
```bash
# Start everything quickly
conda activate chainlit_2
cd "C:\Users\<USER>\Documents\Masters Project"
chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0

# In new terminal
ngrok http 8000 --log=stdout
```

### Support Resources
- **Ngrok Documentation**: https://ngrok.com/docs
- **Chainlit Documentation**: https://docs.chainlit.io
- **Railway Status**: https://status.railway.app

---

## ✅ Success Criteria

Your ngrok backup deployment is successful when:
- ✅ Public URL loads AI Tutor interface
- ✅ Student ID generation works
- ✅ Chat functionality operates normally
- ✅ Database connectivity confirmed
- ✅ Feedback buttons functional
- ✅ File upload capability working
- ✅ Session persistence maintained

**Your backup deployment method is now ready for the June 2nd, 2025 experiment!** 🚀

---

## 🔧 Quick Start Commands Summary

### Emergency Deployment (5 minutes)
```bash
# Terminal 1: Start Chainlit
conda activate chainlit_2
cd "C:\Users\<USER>\Documents\Masters Project"
chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0

# Terminal 2: Start Ngrok
ngrok http 8000 --log=stdout

# Share the https:// URL with participants
```

### Verification Commands
```bash
# Test database connection
python -c "import asyncio, asyncpg, os; asyncio.run(asyncpg.connect(os.getenv('DATABASE_URL')).close())"

# Check application status
curl http://localhost:8000

# Monitor ngrok
curl http://localhost:4040/api/tunnels
```

### Cleanup Commands
```bash
# Stop ngrok
Ctrl+C in ngrok terminal

# Stop Chainlit
Ctrl+C in Chainlit terminal

# Kill any remaining processes
taskkill /f /im ngrok.exe
taskkill /f /im python.exe
```

---

## 📋 Experiment Day Checklist

### 30 Minutes Before Experiment
- [ ] Activate conda environment: `conda activate chainlit_2`
- [ ] Navigate to project: `cd "C:\Users\<USER>\Documents\Masters Project"`
- [ ] Clean database: `python force_clean_database.py`
- [ ] Start Chainlit: `chainlit run solutions/langchain/my_agent_bot.py --port 8000 --host 0.0.0.0`
- [ ] Start ngrok: `ngrok http 8000 --log=stdout`
- [ ] Test public URL functionality
- [ ] Share URL with participants

### During Experiment
- [ ] Monitor ngrok web interface: `http://localhost:4040`
- [ ] Watch for any error messages in terminals
- [ ] Keep Railway URL as backup
- [ ] Be ready to switch deployment methods if needed

### Post-Experiment
- [ ] Export data from Railway database
- [ ] Save ngrok logs if needed
- [ ] Stop all services
- [ ] Document any issues encountered

**This comprehensive backup deployment guide ensures your June 2nd experiment can proceed successfully regardless of Railway availability!** 🎯
