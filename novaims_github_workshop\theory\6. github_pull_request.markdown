# Pull Requests (PRs) 📥👀✅

As we venture into the collaborative features of Git, it's crucial to understand the concept of the Pull Request when using GitHub. 
A Pull Request (PR) is GitHub’s way of streamlining the merging process, encouraging review and discussion before integrating changes.

## What is a Pull Request?

At its core, a PR is a request to merge one branch into another — typically a feature branch into `main` or `dev`. But it's **way more than just a merge**.

When you open a PR, you’re saying:
> “Hey team, I’ve made some changes. Can someone take a look before we add them to the main project?”

In short, it’s like submitting an assignment and asking for peer review — except the feedback is versioned and often emoji-filled. 🧠💬✨

![PR Overview](images/pull_request-ow.png)

<sub>_A visual overview of a PR: new changes, reviews and checks before merging._</sub>

## Why Use Pull Requests?

Here’s why PRs are essential in collaborative projects:

- 🔍 **Code Review**: Teammates can comment on specific lines, ask questions, or suggest improvements.
- ✅ **Approval System**: You can require reviews and approvals before merging.
- ⚙️ **Automation**: GitHub Actions can run tests or checks automatically when a PR is opened or updated.
- 📝 **Documentation**: PRs provide a history of why and how a feature or fix was implemented.
- 📊 **Visibility**: Everyone can see what’s being worked on, ask questions, or raise concerns early.


## Anatomy of a Pull Request

A typical PR contains:

- **Title & Description**: A short summary of what your changes do.
- **Commits**: The changes you’ve made (grouped by commit).
- **Changed Files**: A diff view of what’s different.
- **Discussion Thread**: Comments, questions, and suggestions.
- **Checks**: Any CI tests or workflows that run automatically.
- **Merge Options**: When everything is good to go, the PR can be merged.



## How to Create a PR

1. Push your changes to a branch (not `main`!).
2. Go to the **"Pull Requests"** tab in your GitHub repo.
3. Click **“New Pull Request”**.
4. Choose your source and target branches (e.g., `feature-x` → `main`).
5. Add a descriptive title and message.
6. Hit **Create Pull Request**.

🎉 That’s it! Now others can jump in to review, suggest changes, or approve.

---

## Pro Tips for Good PRs 🌟

- ✅ Keep PRs small and focused — easier to review and test.
- 📸 Use screenshots or gifs if your change involves UI.
- 🔗 Link related issues using `Fixes #123` or `Closes #45`.
- 👋 Be kind and constructive in comments — PRs are a learning tool, not just a checklist.
