import os
import psycopg2
from dotenv import load_dotenv
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def init_database():
    load_dotenv()
    
    try:
        conn = psycopg2.connect(os.getenv("DATABASE_URL"))
        with conn.cursor() as cur:
            # Create tables
            cur.execute("""
                CREATE TABLE IF NOT EXISTS pitch_evaluations (
                    id SERIAL PRIMARY KEY,
                    student_id VARCHAR(255) NOT NULL,
                    pitch_text TEXT,
                    evaluation_score INTEGER,
                    feedback TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS progress_tracking (
                    id SERIAL PRIMARY KEY,
                    student_id VARCHAR(255) UNIQUE NOT NULL,
                    current_step VARCHAR(255),
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            conn.commit()
            logging.info("✅ Database tables created successfully")
    except Exception as e:
        logging.error(f"❌ Database initialization failed: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    init_database()