import os
import asyncpg
import asyncio
from dotenv import load_dotenv

async def drop_tables():
    load_dotenv()
    
    try:
        # Connect to the database
        conn = await asyncpg.connect(os.getenv("DATABASE_URL"))
        
        # Drop tables in reverse order of dependencies
        await conn.execute('DROP TABLE IF EXISTS "Message" CASCADE;')
        await conn.execute('DROP TABLE IF EXISTS "Element" CASCADE;')
        await conn.execute('DROP TABLE IF EXISTS "Step" CASCADE;')
        await conn.execute('DROP TABLE IF EXISTS "Thread" CASCADE;')
        
        print("✅ Tables dropped successfully")
        
        # Close the connection
        await conn.close()
        
    except Exception as e:
        print(f"❌ Error dropping tables: {e}")

if __name__ == "__main__":
    asyncio.run(drop_tables())
