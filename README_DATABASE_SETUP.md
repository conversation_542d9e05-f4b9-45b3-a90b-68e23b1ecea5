# Database Setup for Chainlit Data Persistence

This guide explains how to set up the database for Chainlit data persistence in your AI Elevator Pitch Tutor application.

## Prerequisites

- PostgreSQL installed and running
- Python environment with required packages (asyncpg, dotenv)

## Database Configuration

1. Make sure your `.env` file contains the correct database connection string:

```
DATABASE_URL=postgresql://ai_user:your_password@localhost:5432/ai_tutor
```

## Setting Up the Database Tables

The application requires specific tables for <PERSON><PERSON>'s data persistence. We've provided two scripts to help you manage these tables:

### 1. Initialize Database Tables

Run the following command to create all required tables with camelCase column names (which <PERSON>lit expects):

```
python chainlit_init_camelcase_db.py
```

This will create the following tables:
- Thread
- Step
- Element
- Message

### 2. Reset Database Tables (if needed)

If you need to reset the database tables, run:

```
python drop_tables.py
```

Then re-initialize the tables:

```
python chainlit_init_camelcase_db.py
```

## Running the Application

Always run the application on port 8001 to avoid UI issues:

```
chainlit run solutions\langchain\my_agent_bot.py --port 8001
```

Then access your application at: http://localhost:8001

## Troubleshooting

If you encounter database-related errors:

1. Check that PostgreSQL is running
2. Verify your database connection string in `.env`
3. Try resetting and re-initializing the database tables
4. Check the application logs for specific error messages

## Deployment Considerations

When deploying to Railway or Azure App Services:

1. Set up a PostgreSQL database in your deployment environment
2. Run the initialization script to create the tables
3. Configure the environment variables with the correct DATABASE_URL
