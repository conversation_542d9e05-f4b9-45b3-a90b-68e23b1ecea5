# What is a branch? 

Imagine you're writing a piece of code for some project. Everything works and that is great! 
Now you want to explore an alternative or want to add some feature... and now nothing works! 💀💀💀

## Introduce branching! 

Branches allows you to work on new ideas without affecting the main piece of code. 
Once you're happy with the new addition/s, you can then add them back into the main body.

So, a branch in Git is like a separate path, copied from the original, where you can make changes without affecting the main project. 

And once you are done experimenting you can decide to include the changes, or discard everything! 

It’s a way to work on different tasks separately and keep things organized.

## Why Use Branches?

Normally, each branch has a specific objetive: work on a new feature, fix a bug, refactor some code, etc. 
These branches allow you to experiment and test out ideas safely. 


# Creating and Switching to Branches 🌳

In this section we step into the universe of branches where you create multiple paths for diverse tasks and jump between them seamlessly. 
We will visit the most fundamental commands, and unravel the methods to smoothly navigate through different branches of your project.


## `git branch` : laying down a new path 🛤️

The `git branch` command is your ticket to creating new branches where you can work on different tasks simultaneously.

But it does even more, it is the basis for any operation that you want to perform, from checking information on the branches, to renaming, do deleting.

- `git branch` : it will list all of the branches in your repository.
- `git branch -r` : list all the remote branches.
- `git branch -a` : list both remote-tracking branches and local branches.
- `git branch <branch_name>` : create a new branch named `<branch_name>`.
- `git branch -d <branch_name>` : delete the specified local branch. Use `-D` to force delete unmerged branches.
- `git branch -m <old_name> <new_name>` : rename a branch from `<old_name>` to `<new_name>`.
- `git branch -u <upstream_branch>` : set an upstream branch for the current branch.
- `git branch --show-current` : show the name of the current branch.


## `git checkout` : hopping onto a new branch 🏃‍♂️

The `git checkout` command is your reference point to switching, but also creating, between branches.

- `git checkout <branch>` : switch to the specified branch and update the working directory.
- `git checkout -b <new_branch>` : create a new branch and switch to it.
- `git checkout -b <new_branch> <start_point>` : create a new branch, based on `<start_point>`, and switch to it.
- `git checkout -- <file>` : discard changes in your working directory to the specified `<file>`.
- `git checkout <commit>` : switch to a specific commit, detaching the HEAD.
- `git checkout -` : switch to the branch last checked out.
- `git checkout --orphan <new_branch>` : create a new branch, with no commit history, and switch to it.


<img src="images/branch.webp" alt="Git branch" width="800"/>