import os
import asyncio
import asyncpg
import logging
import bcrypt
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# User credentials for authentication
USERS = [
    {"username": "admin", "password": "admin123", "role": "admin"},
    {"username": "student1", "password": "student123", "role": "user"},
    {"username": "student2", "password": "student123", "role": "user"},
    {"username": "student3", "password": "student123", "role": "user"},
    {"username": "student4", "password": "student123", "role": "user"},
    {"username": "student5", "password": "student123", "role": "user"},
    {"username": "student6", "password": "student123", "role": "user"},
    {"username": "student7", "password": "student123", "role": "user"},
    {"username": "student8", "password": "student123", "role": "user"},
    {"username": "student9", "password": "student123", "role": "user"},
    {"username": "student10", "password": "student123", "role": "user"},
]

async def setup_database():
    """Initialize the database with the Chainlit schema and create users."""
    load_dotenv()
    
    try:
        # Connect to the database
        conn = await asyncpg.connect(os.getenv("DATABASE_URL"))
        
        # Read and execute the SQL schema
        with open('chainlit_schema.sql', 'r') as f:
            schema_sql = f.read()
            await conn.execute(schema_sql)
        
        logging.info("✅ Database schema created successfully")
        
        # Create users with hashed passwords
        for user in USERS:
            # Hash the password
            hashed_password = bcrypt.hashpw(user["password"].encode(), bcrypt.gensalt()).decode()
            
            # Check if user already exists
            existing_user = await conn.fetchrow(
                'SELECT id FROM "User" WHERE username = $1',
                user["username"]
            )
            
            if not existing_user:
                # Insert the user
                await conn.execute(
                    'INSERT INTO "User" (username, password, role) VALUES ($1, $2, $3)',
                    user["username"], hashed_password, user["role"]
                )
                logging.info(f"✅ Created user: {user['username']}")
            else:
                logging.info(f"⚠️ User {user['username']} already exists")
        
        # Close the connection
        await conn.close()
        
        logging.info("✅ Database setup completed successfully")
        
    except Exception as e:
        logging.error(f"❌ Database setup failed: {e}")

if __name__ == "__main__":
    asyncio.run(setup_database())
