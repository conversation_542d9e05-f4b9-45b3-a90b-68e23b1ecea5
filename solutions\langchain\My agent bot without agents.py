import chainlit as cl
from langchain.callbacks.base import BaseCallbackHandler
from langchain.chains import <PERSON><PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain_community.llms import Ollama
from langchain_core.prompts import PromptTemplate
from langchain.callbacks.manager import CallbackManager


class StreamHandler(BaseCallbackHandler):
    def __init__(self, message_id: str):
        self.message_id = message_id
        self.content = ""

    async def on_llm_new_token(self, token: str, **kwargs):
        self.content += token
        await cl.Message(id=self.message_id, content=self.content).update()

    async def on_llm_end(self, response, **kwargs):
        pass

    async def on_llm_error(self, error, **kwargs):
        await cl.Message(id=self.message_id, content=f"Error: {error}").update()


# Load DeepSeek model using Ollama
# Choose a larger model (14B or 67B recommended for better quality)
# Ensure you have enough resources (RAM and GPU) to run the larger model
llm = Ollama(
    model="tinyllama:1.1b-chat",#"llama2:7b-chat-q2_K", #"deepseek-r1:1.5b",  # Or deepseek-r1:67b if you can run it
    temperature=0.7,
    callback_manager=CallbackManager([])
)

template = """
[INST] <<SYS>>
You are a helpful, respectful, and honest AI assistant.
Always provide clear and concise answers.
<</SYS>>
User:
{instruction}[/INST]"""

prompt = PromptTemplate(template=template, input_variables=["instruction"])


@cl.on_chat_start
def on_chat_start():
    memory = ConversationBufferMemory(memory_key="context", input_key="instruction")
    llm_chain = LLMChain(prompt=prompt, llm=llm, verbose=False, memory=memory)
    cl.user_session.set("llm_chain", llm_chain)


@cl.on_message
async def on_message(message: cl.Message):
    llm_chain = cl.user_session.get("llm_chain")

    msg = cl.Message(content="")
    await msg.send()

    stream_handler = StreamHandler(message_id=msg.id)

    try:
        res = await llm_chain.ainvoke(
            {"instruction": message.content},
            config={"callbacks": [stream_handler, cl.AsyncLangchainCallbackHandler()]}
        )
    except Exception as e:
        await cl.Message(content=f"An error occurred: {e}").send()
        return



@cl.on_message
async def handle_message(message: cl.Message):
    selected_agent = cl.user_session.get("agent")

    if message.content.lower() in ["/mentor", "/peer", "/progress"]:
        cl.user_session.set("agent", message.content[1:])
        await cl.Message(f"🔹 Switched to **{message.content[1:].capitalize()} Agent**").send()
        return
    
    if "switch" in message.content.lower():
        if "peer" in message.content.lower():
            cl.user_session.set("agent", "peer")
        elif "mentor" in message.content.lower():
            cl.user_session.set("agent", "mentor")
        elif "progress" in message.content.lower():
            cl.user_session.set("agent", "progress")
        await cl.Message(f"🔹 Auto-switched to **{cl.user_session.get('agent').capitalize()} Agent**").send()
        return

    memory[selected_agent].add_user_message(message.content)
    logging.info(f"📝 User Input to {selected_agent}: {message.content}")
    
    try:
        response = await agents[selected_agent].ainvoke(
            {"question": message.content, "context": memory[selected_agent].messages},
            config={"callbacks": [StreamHandler(message.id), cl.AsyncLangchainCallbackHandler()]}
        )

        # Extract and log AI response
        if isinstance(response, AIMessage):
            ai_text = response.content
        elif isinstance(response, dict) and "content" in response:
            ai_text = response["content"]

        else:
            ai_text = response.text

        logging.info(f"🔹 Processed AI Response: {ai_text}")    
        
        # Ensure a valid response is returned
        if not ai_text or ai_text.strip() == "":
            ai_text = "I'm sorry, I couldn't generate a response. Please try again."
        
        memory[selected_agent].add_ai_message(ai_text)
        await cl.Message(content=ai_text).send()
    
    except Exception as e:
        logging.error(f"❌ Error in OpenAI API Call: {e}")
        await cl.Message(content=f"An error occurred: {e}").send()

@cl.on_stop
async def stop_execution():
    cl.user_session.clear()  
    for mem in memory.values():
        mem.clear()
    await cl.Message("Session stopped by user.").send()
