import os
import asyncio
import asyncpg
import sys

# Railway PostgreSQL connection string
# This should be the actual Railway PostgreSQL URL
RAILWAY_DB_URL = "postgresql://postgres:<EMAIL>:5432/railway"

async def check_feedback_schema():
    """Check the schema of the Feedback table in Railway."""
    try:
        # Connect to the database
        print(f"Connecting to database...")
        conn = await asyncpg.connect(RAILWAY_DB_URL)
        
        # Check if the Feedback table exists
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        
        print("Tables in database:")
        for table in tables:
            print(f"- {table['table_name']}")
        
        # Check the schema of the Feedback table
        feedback_schema = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'Feedback'
        """)
        
        if not feedback_schema:
            # Try lowercase
            feedback_schema = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'feedback'
            """)
        
        if feedback_schema:
            print("\nFeedback table schema:")
            for column in feedback_schema:
                print(f"- {column['column_name']} ({column['data_type']}, nullable: {column['is_nullable']})")
        else:
            print("\nFeedback table not found!")
            
        # Close the connection
        await conn.close()
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(check_feedback_schema())
