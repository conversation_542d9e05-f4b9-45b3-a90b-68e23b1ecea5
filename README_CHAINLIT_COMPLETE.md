# Chainlit Setup with Complete Database Schema and Authentication

This guide explains how to set up Chainlit with a complete PostgreSQL database schema and password authentication.

## Prerequisites

- PostgreSQL installed and running
- Python environment with required packages (asyncpg, bcrypt, dotenv)

## Setup Steps

### 1. Database Configuration

1. Make sure your `.env` file contains the correct database connection string:

```
DATABASE_URL=postgresql://ai_user:your_password@localhost:5432/ai_tutor
```

2. Run the complete database setup script:

```
python setup_chainlit_db_complete.py
```

This script:
- Drops any existing tables to ensure a clean setup
- Creates all necessary tables with the complete schema matching Chainlit's requirements
- Creates user accounts for authentication (admin and 10 students)

### 2. Authentication Configuration

The application is configured to use password authentication with the following users:

- Admin: username `admin`, password `admin123`
- Students: usernames `student1` through `student10`, password `student123`

These credentials are stored in `users.yaml` and referenced in `chainlit.toml`.

### 3. Running the Application

Always run the application on port 8001 to ensure proper UI rendering:

```
chainlit run solutions\langchain\my_agent_bot.py --port 8001
```

Then access your application at: http://localhost:8001

## Complete Database Schema

The complete schema includes all columns required by Chainlit:

- **Thread**: For conversation threads
- **Step**: For individual steps in a conversation (with all required fields like `showInput`)
- **Element**: For attachments and other elements
- **Message**: For chat messages
- **Feedback**: For user feedback
- **User**: For authentication

## Azure Blob Storage (Optional)

To enable file storage with Azure Blob Storage:

1. Create an Azure Storage account and container
2. Update the `.env` file with your Azure credentials:

```
BUCKET_NAME=my-container
APP_AZURE_STORAGE_ACCOUNT=your-storage-account
APP_AZURE_STORAGE_ACCESS_KEY=your-access-key
APP_AZURE_STORAGE_CONNECTION_STRING=your-connection-string
```

## Troubleshooting

If you encounter database-related errors:

1. Check the error message to identify missing columns or tables
2. Update the schema in `chainlit_schema_complete.sql` if needed
3. Run `setup_chainlit_db_complete.py` again to apply the changes
4. Check the application logs for specific error messages

## References

- [Official Chainlit Data Layer](https://github.com/Chainlit/chainlit-datalayer)
- [Chainlit Documentation](https://docs.chainlit.io/data-layers/official)
- [Chainlit Authentication](https://docs.chainlit.io/authentication/password)
