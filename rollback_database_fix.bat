@echo off
echo ========================================
echo Railway Database Fix Rollback Script
echo ========================================
echo.

echo ⚠️  WARNING: This will rollback your database fix deployment
echo.
echo 📋 This script will:
echo   1. Switch to the backup branch (backup-before-db-fix)
echo   2. Create a rollback branch
echo   3. Push the rollback to GitHub
echo   4. Provide Railway deployment instructions
echo.

echo ❓ Are you sure you want to rollback the database fix?
echo    Type 'YES' to continue or any other key to cancel:
set /p confirm=

if not "%confirm%"=="YES" (
    echo ❌ Rollback cancelled by user
    pause
    exit /b 0
)

echo.
echo 🔄 Step 1: Checking if backup branch exists...
git show-ref --verify --quiet refs/heads/backup-before-db-fix
if %errorlevel% neq 0 (
    echo ❌ Backup branch 'backup-before-db-fix' not found!
    echo    Please ensure you created a backup before deploying the fix.
    pause
    exit /b 1
)

echo ✅ Backup branch found
echo.

echo 🔄 Step 2: Switching to backup branch...
git checkout backup-before-db-fix
if %errorlevel% neq 0 (
    echo ❌ Failed to switch to backup branch
    pause
    exit /b 1
)

echo ✅ Switched to backup branch
echo.

echo 🔄 Step 3: Creating rollback branch...
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "rollback_branch=rollback-%YYYY%%MM%%DD%"

git checkout -b %rollback_branch%
if %errorlevel% neq 0 (
    echo ❌ Failed to create rollback branch
    pause
    exit /b 1
)

echo ✅ Created rollback branch: %rollback_branch%
echo.

echo 🔄 Step 4: Pushing rollback branch to GitHub...
git push origin %rollback_branch%
if %errorlevel% neq 0 (
    echo ❌ Failed to push rollback branch
    pause
    exit /b 1
)

echo ✅ Rollback branch pushed to GitHub
echo.

echo 🔄 Step 5: Updating main branch (SAFE METHOD)...
echo    Creating rollback as new main branch...

git push origin %rollback_branch%:main-rollback
if %errorlevel% neq 0 (
    echo ❌ Failed to create main-rollback branch
    pause
    exit /b 1
)

echo ✅ Created main-rollback branch on GitHub
echo.

echo 🎉 ROLLBACK PREPARATION COMPLETED!
echo.
echo 📊 Summary:
echo   ✅ Switched to backup branch: backup-before-db-fix
echo   ✅ Created rollback branch: %rollback_branch%
echo   ✅ Created main-rollback branch on GitHub
echo.
echo 🔄 NEXT STEPS - Railway Deployment:
echo.
echo   Option 1 - Change Railway deployment branch:
echo   1. Go to Railway Dashboard → Your Project
echo   2. Click Settings → Environment
echo   3. Change deployment branch from 'main' to 'main-rollback'
echo   4. Click 'Deploy' to redeploy from rollback
echo.
echo   Option 2 - Force update main branch (DESTRUCTIVE):
echo   git push origin %rollback_branch%:main --force
echo   (This will overwrite the current main branch)
echo.
echo ⚠️  IMPORTANT NOTES:
echo   - Your database fix changes are preserved in the main branch
echo   - You can switch back to the fix later if needed
echo   - The rollback restores your app to the pre-fix state
echo   - Test your app after rollback to ensure it works correctly
echo.
echo 📝 To switch back to the database fix later:
echo   git checkout main
echo   git push origin main --force
echo.

pause
