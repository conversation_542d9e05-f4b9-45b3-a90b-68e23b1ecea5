CREATE TABLE IF NOT EXISTS pitch_evaluations (
    id SERIAL PRIMARY KEY,
    student_id VARCHAR(255) NOT NULL,
    pitch_text TEXT,
    evaluation_score INTEGER,
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS progress_tracking (
    id SERIAL PRIMARY KEY,
    student_id VARCHAR(255) UNIQUE NOT NULL,
    current_step VARCHAR(255),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Thread table
CREATE TABLE IF NOT EXISTS "Thread" (
    id UUID PRIMARY KEY,
    name TEXT
);

-- Step table
CREATE TABLE IF NOT EXISTS "Step" (
    id UUID PRIMARY KEY,
    thread_id UUID REFERENCES "Thread"(id),
    name TEXT,
    type TEXT,
    input TEXT,
    output TEXT
);

-- Verify tables were created
\dt

