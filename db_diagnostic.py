#!/usr/bin/env python3
"""
Database diagnostic script to check Railway PostgreSQL connection and schema
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def check_database_connection():
    """Check database connection and schema"""

    # Railway internal URL (primary) - only works from within Railway
    railway_url = "postgresql://postgres:<EMAIL>:5432/railway"

    # Try to get external Railway URL (you need to get this from Railway dashboard)
    # Format: ***************************************************************************/railway
    external_railway_url = input("Please enter your Railway external database URL (from Railway dashboard): ").strip()

    # Fallback to environment variable
    env_url = os.getenv("DATABASE_URL")

    print("🔍 Database Connection Diagnostic")
    print("=" * 50)

    for url_name, url in [("Railway Internal", railway_url), ("Railway External", external_railway_url), ("Environment Variable", env_url)]:
        if not url:
            print(f"❌ {url_name}: URL not available")
            continue

        try:
            print(f"\n🔗 Testing {url_name} connection...")
            print(f"URL: {url}")

            conn = await asyncpg.connect(url)
            print(f"✅ {url_name}: Connection successful!")

            # Check existing tables
            print(f"\n📋 Checking tables in {url_name}...")
            tables_query = """
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """
            tables = await conn.fetch(tables_query)
            print(f"Found {len(tables)} tables:")
            for table in tables:
                print(f"  - {table['table_name']}")

            # Check specific table schemas
            for table_name in ['element', 'message', 'feedback', 'thread', 'user', 'step', 'student_sessions', 'pitch_evaluations']:
                print(f"\n🔍 Checking schema for table '{table_name}'...")
                try:
                    schema_query = """
                        SELECT column_name, data_type, is_nullable, column_default
                        FROM information_schema.columns
                        WHERE table_name = $1 AND table_schema = 'public'
                        ORDER BY ordinal_position;
                    """
                    columns = await conn.fetch(schema_query, table_name)
                    if columns:
                        print(f"  Table '{table_name}' columns:")
                        for col in columns:
                            print(f"    - {col['column_name']} ({col['data_type']}) {'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'}")
                    else:
                        print(f"  ❌ Table '{table_name}' does not exist")
                except Exception as e:
                    print(f"  ❌ Error checking table '{table_name}': {e}")

            # Check row counts
            print(f"\n📊 Checking row counts...")
            for table_name in ['element', 'message', 'feedback', 'thread', 'user', 'step', 'student_sessions', 'pitch_evaluations']:
                try:
                    count_query = f"SELECT COUNT(*) as count FROM {table_name};"
                    result = await conn.fetchrow(count_query)
                    print(f"  - {table_name}: {result['count']} rows")
                except Exception as e:
                    print(f"  - {table_name}: Error - {e}")

            await conn.close()
            print(f"✅ {url_name}: Connection closed successfully")

            # If we successfully connected, we can stop here
            return True

        except Exception as e:
            print(f"❌ {url_name}: Connection failed - {e}")
            continue

    return False

async def create_missing_tables():
    """Create missing tables and fix schema issues"""

    railway_url = "postgresql://postgres:<EMAIL>:5432/railway"

    try:
        conn = await asyncpg.connect(railway_url)
        print("\n🔧 Creating/fixing database schema...")

        # Create student_sessions table if it doesn't exist
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS student_sessions (
                id SERIAL PRIMARY KEY,
                student_id VARCHAR(8) UNIQUE NOT NULL,
                current_step_index INTEGER DEFAULT 0,
                total_interactions INTEGER DEFAULT 0,
                completed_steps INTEGER DEFAULT 0,
                last_message_content TEXT,
                last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        print("✅ student_sessions table created/verified")

        # Create pitch_evaluations table if it doesn't exist
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS pitch_evaluations (
                id SERIAL PRIMARY KEY,
                student_id VARCHAR(8) NOT NULL,
                step_name VARCHAR(255) NOT NULL,
                score INTEGER,
                feedback TEXT,
                evaluation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                FOREIGN KEY (student_id) REFERENCES student_sessions(student_id) ON DELETE CASCADE
            );
        """)
        print("✅ pitch_evaluations table created/verified")

        # Check if feedback table exists and has the right schema
        feedback_columns = await conn.fetch("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'feedback' AND table_schema = 'public';
        """)

        if feedback_columns:
            column_names = [col['column_name'] for col in feedback_columns]
            print(f"📋 Feedback table columns: {column_names}")

            # Add missing 'name' column if it doesn't exist
            if 'name' not in column_names:
                await conn.execute("ALTER TABLE feedback ADD COLUMN name VARCHAR(255);")
                print("✅ Added 'name' column to feedback table")

        await conn.close()
        print("✅ Database schema update completed")
        return True

    except Exception as e:
        print(f"❌ Error updating database schema: {e}")
        return False

if __name__ == "__main__":
    print("Starting database diagnostic...")

    # Run connection check
    asyncio.run(check_database_connection())

    # Ask user if they want to create missing tables
    response = input("\n🤔 Would you like to create/fix missing tables? (y/n): ")
    if response.lower() in ['y', 'yes']:
        asyncio.run(create_missing_tables())

    print("\n✅ Diagnostic complete!")
