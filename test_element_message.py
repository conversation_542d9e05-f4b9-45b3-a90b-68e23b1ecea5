#!/usr/bin/env python3
"""
Test Element and Message table functionality
"""
import asyncio
import asyncpg
import uuid
from datetime import datetime

async def test_element_message_functionality():
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(url)
        print("✅ Connected to Railway database")
        
        # Create test thread and user for realistic testing
        thread_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        print(f"🧪 Testing with Thread ID: {thread_id}")
        print(f"🧪 Testing with User ID: {user_id}")
        
        # Test 1: Insert a test thread
        print("\n📋 Test 1: Creating test thread...")
        try:
            await conn.execute("""
                INSERT INTO "Thread" (id, name, "userId", "userIdentifier", "createdAt")
                VALUES ($1, $2, $3, $4, $5);
            """, thread_id, "Test Thread for Element/Message", user_id, "test_user_12345", datetime.now())
            print("✅ Test thread created successfully")
        except Exception as e:
            print(f"❌ Error creating thread: {e}")
            return False
        
        # Test 2: Insert Element (simulating file upload)
        print("\n📁 Test 2: Testing Element insertion (file upload simulation)...")
        try:
            element_id = await conn.fetchval("""
                INSERT INTO "Element" (type, name, url, mime, "threadId", size, display)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id;
            """, "file", "student_pitch.pdf", "/uploads/student_pitch.pdf", "application/pdf", thread_id, 1024, "inline")
            
            print(f"✅ Element inserted successfully with ID: {element_id}")
            
            # Verify element was inserted
            element_count = await conn.fetchval('SELECT COUNT(*) FROM "Element";')
            print(f"✅ Element table now has {element_count} rows")
            
        except Exception as e:
            print(f"❌ Error inserting element: {e}")
            return False
        
        # Test 3: Insert Message (simulating chat message)
        print("\n💬 Test 3: Testing Message insertion (chat message simulation)...")
        try:
            message_id = await conn.fetchval("""
                INSERT INTO "Message" (content, "threadId", "authorId", indent)
                VALUES ($1, $2, $3, $4)
                RETURNING id;
            """, "Hello! I've uploaded my elevator pitch. Can you please evaluate it?", thread_id, user_id, 0)
            
            print(f"✅ Message inserted successfully with ID: {message_id}")
            
            # Insert AI response message
            ai_message_id = await conn.fetchval("""
                INSERT INTO "Message" (content, "threadId", "authorId", "parentId", indent)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id;
            """, "I've received your pitch! Let me evaluate it for you...", thread_id, None, message_id, 1)
            
            print(f"✅ AI response message inserted with ID: {ai_message_id}")
            
            # Verify messages were inserted
            message_count = await conn.fetchval('SELECT COUNT(*) FROM "Message";')
            print(f"✅ Message table now has {message_count} rows")
            
        except Exception as e:
            print(f"❌ Error inserting message: {e}")
            return False
        
        # Test 4: Query the data to verify relationships
        print("\n🔍 Test 4: Verifying data relationships...")
        try:
            # Get thread with related data
            thread_data = await conn.fetchrow("""
                SELECT t.id, t.name, t."userIdentifier",
                       COUNT(DISTINCT e.id) as element_count,
                       COUNT(DISTINCT m.id) as message_count
                FROM "Thread" t
                LEFT JOIN "Element" e ON e."threadId" = t.id
                LEFT JOIN "Message" m ON m."threadId" = t.id
                WHERE t.id = $1
                GROUP BY t.id, t.name, t."userIdentifier";
            """, thread_id)
            
            if thread_data:
                print(f"✅ Thread '{thread_data['name']}' has:")
                print(f"   - {thread_data['element_count']} elements")
                print(f"   - {thread_data['message_count']} messages")
                print(f"   - User: {thread_data['userIdentifier']}")
            else:
                print("❌ Could not find thread data")
                return False
            
        except Exception as e:
            print(f"❌ Error querying relationships: {e}")
            return False
        
        # Test 5: Test Feedback insertion with proper relationships
        print("\n👍 Test 5: Testing Feedback with Element relationship...")
        try:
            feedback_id = await conn.fetchval("""
                INSERT INTO "Feedback" (name, value, comment, "forId")
                VALUES ($1, $2, $3, $4)
                RETURNING id;
            """, "pitch_quality", 5, "Great pitch! Very clear and compelling.", element_id)
            
            print(f"✅ Feedback linked to element inserted with ID: {feedback_id}")
            
            # Verify feedback relationship
            feedback_data = await conn.fetchrow("""
                SELECT f.name, f.value, f.comment, e.name as element_name
                FROM "Feedback" f
                JOIN "Element" e ON f."forId" = e.id
                WHERE f.id = $1;
            """, feedback_id)
            
            if feedback_data:
                print(f"✅ Feedback '{feedback_data['name']}' (score: {feedback_data['value']}) linked to element '{feedback_data['element_name']}'")
            
        except Exception as e:
            print(f"❌ Error testing feedback relationships: {e}")
            return False
        
        # Clean up test data
        print("\n🧹 Cleaning up test data...")
        try:
            await conn.execute('DELETE FROM "Feedback" WHERE "forId" = $1;', element_id)
            await conn.execute('DELETE FROM "Message" WHERE "threadId" = $1;', thread_id)
            await conn.execute('DELETE FROM "Element" WHERE "threadId" = $1;', thread_id)
            await conn.execute('DELETE FROM "Thread" WHERE id = $1;', thread_id)
            print("✅ Test data cleaned up")
        except Exception as e:
            print(f"⚠️ Warning: Could not clean up all test data: {e}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Element and Message Functionality...")
    print("=" * 50)
    
    success = asyncio.run(test_element_message_functionality())
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Element table: Ready for file uploads")
        print("✅ Message table: Ready for chat messages") 
        print("✅ Feedback table: Ready for user feedback")
        print("✅ Relationships: Working correctly")
        print("\n🎯 Your database is now fully ready for the June 2nd experiment!")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
