#!/usr/bin/env python3
"""
Quick test to verify feedback functionality works
"""
import asyncio
import asyncpg

async def test_feedback():
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(url)
        print("✅ Connected to Railway database")
        
        # Test inserting feedback
        test_id = await conn.fetchval("""
            INSERT INTO "Feedback" (name, value, comment, "forId")
            VALUES ($1, $2, $3, $4)
            RETURNING id;
        """, "test_feedback", 5, "Test feedback from verification", None)
        
        print(f"✅ SUCCESS: Feedback inserted with ID: {test_id}")
        
        # Clean up
        await conn.execute('DELETE FROM "Feedback" WHERE id = $1;', test_id)
        print("✅ Test feedback cleaned up")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_feedback())
    if success:
        print("\n🎉 FEEDBACK FUNCTIONALITY IS WORKING!")
        print("✅ Your Railway database schema is fixed!")
        print("✅ Students can now use feedback buttons without errors!")
    else:
        print("\n❌ Feedback test failed")
