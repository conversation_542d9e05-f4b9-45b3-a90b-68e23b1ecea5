#!/usr/bin/env python3
"""
Fix message storage to ensure messages are stored in Message table
"""
import asyncio
import asyncpg
import uuid
from datetime import datetime

async def fix_message_storage():
    """Copy recent step data to Message table and set up proper message storage."""
    
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    
    try:
        conn = await asyncpg.connect(url)
        print("🔧 FIXING MESSAGE STORAGE")
        print("=" * 40)
        
        # 1. Get recent user and assistant messages from Step table
        print("📋 Extracting messages from Step table...")
        
        recent_messages = await conn.fetch("""
            SELECT s.id, s."threadId", s.name, s.type, s.input, s.output, s."createdAt", t."userIdentifier"
            FROM "Step" s
            JOIN "Thread" t ON s."threadId" = t.id
            WHERE s.type IN ('user_message', 'assistant_message', 'run')
            AND s."createdAt" >= NOW() - INTERVAL '7 days'
            ORDER BY s."createdAt" ASC;
        """)
        
        print(f"Found {len(recent_messages)} recent message-related steps")
        
        # 2. Convert Steps to Messages
        print("💬 Converting Steps to Messages...")
        
        converted_count = 0
        for step in recent_messages:
            try:
                # Determine message content and author
                if step['type'] == 'user_message':
                    content = step['input'] or step['name'] or "User message"
                    author = step['userIdentifier'] or "User"
                    author_is_user = True
                elif step['type'] == 'assistant_message':
                    content = step['output'] or step['name'] or "Assistant message"
                    author = "AI Tutor"
                    author_is_user = False
                elif step['type'] == 'run':
                    # Skip run steps as they're not actual messages
                    continue
                else:
                    continue
                
                # Check if message already exists
                existing = await conn.fetchval("""
                    SELECT id FROM "Message" 
                    WHERE "threadId" = $1 AND content = $2 AND "createdAt" = $3;
                """, step['threadId'], content, step['createdAt'])
                
                if not existing:
                    # Insert into Message table
                    message_id = await conn.fetchval("""
                        INSERT INTO "Message" ("threadId", content, author, "authorIsUser", "createdAt", "updatedAt")
                        VALUES ($1, $2, $3, $4, $5, $6)
                        RETURNING id;
                    """, step['threadId'], content, author, author_is_user, step['createdAt'], step['createdAt'])
                    
                    converted_count += 1
                    print(f"  ✅ Converted: {author} - {content[:50]}...")
                
            except Exception as e:
                print(f"  ⚠️ Error converting step {step['id']}: {e}")
        
        print(f"✅ Converted {converted_count} steps to messages")
        
        # 3. Check current message count
        message_count = await conn.fetchval('SELECT COUNT(*) FROM "Message";')
        print(f"📊 Message table now has {message_count} rows")
        
        # 4. Create a trigger to automatically copy future messages
        print("🔧 Setting up automatic message copying...")
        
        try:
            # Create function to copy messages
            await conn.execute("""
                CREATE OR REPLACE FUNCTION copy_step_to_message()
                RETURNS TRIGGER AS $$
                BEGIN
                    -- Only process user_message and assistant_message types
                    IF NEW.type IN ('user_message', 'assistant_message') THEN
                        INSERT INTO "Message" (
                            "threadId", 
                            content, 
                            author, 
                            "authorIsUser", 
                            "createdAt", 
                            "updatedAt"
                        )
                        VALUES (
                            NEW."threadId",
                            CASE 
                                WHEN NEW.type = 'user_message' THEN COALESCE(NEW.input, NEW.name, 'User message')
                                WHEN NEW.type = 'assistant_message' THEN COALESCE(NEW.output, NEW.name, 'Assistant message')
                            END,
                            CASE 
                                WHEN NEW.type = 'user_message' THEN 'User'
                                WHEN NEW.type = 'assistant_message' THEN 'AI Tutor'
                            END,
                            CASE 
                                WHEN NEW.type = 'user_message' THEN TRUE
                                WHEN NEW.type = 'assistant_message' THEN FALSE
                            END,
                            NEW."createdAt",
                            NEW."updatedAt"
                        )
                        ON CONFLICT DO NOTHING;
                    END IF;
                    
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # Create trigger
            await conn.execute("""
                DROP TRIGGER IF EXISTS step_to_message_trigger ON "Step";
                CREATE TRIGGER step_to_message_trigger
                    AFTER INSERT ON "Step"
                    FOR EACH ROW
                    EXECUTE FUNCTION copy_step_to_message();
            """)
            
            print("✅ Automatic message copying trigger created")
            
        except Exception as e:
            print(f"⚠️ Could not create trigger: {e}")
        
        # 5. Test the trigger
        print("🧪 Testing automatic message copying...")
        
        try:
            # Create a test thread
            test_thread_id = await conn.fetchval("""
                INSERT INTO "Thread" (name, "userIdentifier")
                VALUES ($1, $2)
                RETURNING id;
            """, "Test Message Storage", "test_user_trigger")
            
            # Insert a test step (simulating what Chainlit does)
            test_step_id = await conn.fetchval("""
                INSERT INTO "Step" ("threadId", name, type, input, output)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id;
            """, test_thread_id, "Test User Message", "user_message", "Hello, this is a test message", None)
            
            # Insert assistant response step
            assistant_step_id = await conn.fetchval("""
                INSERT INTO "Step" ("threadId", name, type, input, output)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id;
            """, test_thread_id, "Test Assistant Response", "assistant_message", None, "Hello! I received your test message.")
            
            # Check if messages were automatically created
            test_messages = await conn.fetch("""
                SELECT content, author, "authorIsUser"
                FROM "Message"
                WHERE "threadId" = $1
                ORDER BY "createdAt";
            """, test_thread_id)
            
            if len(test_messages) >= 2:
                print("✅ Trigger working! Messages automatically created:")
                for msg in test_messages:
                    print(f"  - {msg['author']}: {msg['content']}")
            else:
                print("⚠️ Trigger may not be working properly")
            
            # Clean up test data
            await conn.execute('DELETE FROM "Message" WHERE "threadId" = $1;', test_thread_id)
            await conn.execute('DELETE FROM "Step" WHERE "threadId" = $1;', test_thread_id)
            await conn.execute('DELETE FROM "Thread" WHERE id = $1;', test_thread_id)
            print("✅ Test data cleaned up")
            
        except Exception as e:
            print(f"❌ Error testing trigger: {e}")
        
        # 6. Final status
        final_message_count = await conn.fetchval('SELECT COUNT(*) FROM "Message";')
        print(f"\n📊 FINAL STATUS:")
        print(f"  - Message table: {final_message_count} rows")
        print(f"  - Automatic copying: {'✅ Enabled' if converted_count > 0 else '⚠️ Check manually'}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing message storage: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Message Storage Fix...")
    
    success = asyncio.run(fix_message_storage())
    
    if success:
        print("\n🎉 MESSAGE STORAGE FIX COMPLETED!")
        print("✅ Historical messages converted from Steps to Messages")
        print("✅ Automatic message copying enabled for future messages")
        print("✅ Your Message table will now track all conversations")
        print("\n🎯 Ready for June 2nd experiment with full message tracking!")
    else:
        print("\n❌ Message storage fix failed. Check the logs above for details.")
