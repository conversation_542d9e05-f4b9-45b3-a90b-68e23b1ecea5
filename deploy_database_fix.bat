@echo off
echo ========================================
echo Railway Database Fix Deployment Script
echo ========================================
echo.

echo 📋 This script will:
echo   1. Create a backup branch (backup-before-db-fix)
echo   2. Deploy the database schema fix
echo   3. Provide rollback instructions
echo.

pause

echo 🔄 Step 1: Creating backup branch...
git checkout -b backup-before-db-fix
if %errorlevel% neq 0 (
    echo ❌ Failed to create backup branch
    pause
    exit /b 1
)

echo ✅ Backup branch created: backup-before-db-fix
echo.

echo 🔄 Step 2: Switching back to main branch...
git checkout main
if %errorlevel% neq 0 (
    echo ❌ Failed to switch to main branch
    pause
    exit /b 1
)

echo ✅ Switched to main branch
echo.

echo 🔄 Step 3: Adding all changes...
git add .
if %errorlevel% neq 0 (
    echo ❌ Failed to add changes
    pause
    exit /b 1
)

echo ✅ Changes added to staging
echo.

echo 🔄 Step 4: Committing changes...
git commit -m "Fix Railway PostgreSQL schema issues for Chainlit data persistence

- Add fix_railway_schema.py to resolve Feedback table schema mismatch
- Add missing 'name' and 'forId' columns to Feedback table
- Create/update all Chainlit tables with correct schema
- Add performance indexes and custom application tables
- Update .railwayrc to run schema fix on deployment
- Add verification script and comprehensive documentation

Fixes:
- Column 'name' of relation 'Feedback' does not exist error
- Empty Element, Message, and Feedback tables
- Data persistence issues in Railway deployment

Version: 2.1 - Railway PostgreSQL Schema Fix"

if %errorlevel% neq 0 (
    echo ❌ Failed to commit changes
    pause
    exit /b 1
)

echo ✅ Changes committed successfully
echo.

echo 🔄 Step 5: Pushing to GitHub...
git push origin main
if %errorlevel% neq 0 (
    echo ❌ Failed to push to GitHub
    echo 🔄 Trying to push backup branch first...
    git push origin backup-before-db-fix
    echo.
    echo ❌ Main push failed. Please check your connection and try again.
    pause
    exit /b 1
)

echo ✅ Successfully pushed to GitHub!
echo.

echo 🔄 Step 6: Pushing backup branch...
git push origin backup-before-db-fix
if %errorlevel% neq 0 (
    echo ⚠️ Warning: Failed to push backup branch, but main deployment succeeded
) else (
    echo ✅ Backup branch pushed successfully
)

echo.
echo 🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!
echo.
echo 📊 Summary:
echo   ✅ Backup created: backup-before-db-fix branch
echo   ✅ Database fix deployed to main branch
echo   ✅ Railway will automatically deploy the changes
echo.
echo 🔄 Railway Deployment Status:
echo   - Go to your Railway dashboard to monitor deployment
echo   - Look for schema fix success messages in logs
echo   - Test feedback functionality after deployment
echo.
echo 🔙 ROLLBACK INSTRUCTIONS (if needed):
echo   If something goes wrong, you can rollback using:
echo.
echo   git checkout backup-before-db-fix
echo   git checkout -b main-rollback
echo   git push origin main-rollback --force
echo.
echo   Then in Railway dashboard, deploy from main-rollback branch
echo.
echo 📝 Next Steps:
echo   1. Monitor Railway deployment logs
echo   2. Test your application feedback functionality
echo   3. Run verify_fix.py to confirm the fix worked
echo   4. Prepare for your April 15, 2025 experiment
echo.

pause
