#!/usr/bin/env python3
"""
Investigate Railway PostgreSQL database for duplicate tables and schema issues
"""
import asyncio
import asyncpg

async def investigate_database():
    url = "postgresql://postgres:<EMAIL>:20482/railway"
    conn = await asyncpg.connect(url)
    
    print("🔍 INVESTIGATING DATABASE TABLES...")
    print("=" * 50)
    
    # Get all tables
    tables = await conn.fetch("""
        SELECT table_name, table_schema 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
    """)
    
    print(f"📋 Found {len(tables)} tables:")
    table_names = []
    for table in tables:
        table_name = table['table_name']
        table_names.append(table_name)
        print(f"  - {table_name}")
    
    print()
    
    # Check for duplicate-like tables (case variations)
    print("⚠️  CHECKING FOR DUPLICATE TABLES:")
    seen_lower = {}
    duplicates = []
    
    for name in table_names:
        lower_name = name.lower()
        if lower_name in seen_lower:
            duplicates.append((seen_lower[lower_name], name))
            print(f"  🔴 DUPLICATE: '{seen_lower[lower_name]}' vs '{name}'")
        else:
            seen_lower[lower_name] = name
    
    if not duplicates:
        print("  ✅ No duplicate tables found")
    
    print()
    
    # Check row counts for key tables
    print("📊 ROW COUNTS:")
    key_tables = ['Element', 'element', 'Message', 'message', 'Feedback', 'feedback', 
                  'Thread', 'thread', 'User', 'user', 'Step', 'step',
                  'pitch_evaluations', 'student_sessions']
    
    for table_name in key_tables:
        if table_name in table_names:
            try:
                count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table_name}";')
                print(f"  - {table_name}: {count} rows")
            except Exception as e:
                print(f"  - {table_name}: Error - {e}")
    
    print()
    
    # Check Element table schema
    element_tables = [t for t in table_names if t.lower() == 'element']
    for table_name in element_tables:
        print(f"🔍 {table_name.upper()} TABLE SCHEMA:")
        try:
            element_columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = $1 AND table_schema = 'public'
                ORDER BY ordinal_position;
            """, table_name)
            
            for col in element_columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                print(f"  - {col['column_name']} ({col['data_type']}) {nullable}")
        except Exception as e:
            print(f"  Error reading schema: {e}")
        print()
    
    # Check Message table schema  
    message_tables = [t for t in table_names if t.lower() == 'message']
    for table_name in message_tables:
        print(f"🔍 {table_name.upper()} TABLE SCHEMA:")
        try:
            message_columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = $1 AND table_schema = 'public'
                ORDER BY ordinal_position;
            """, table_name)
            
            for col in message_columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                print(f"  - {col['column_name']} ({col['data_type']}) {nullable}")
        except Exception as e:
            print(f"  Error reading schema: {e}")
        print()
    
    # Check recent pitch_evaluations entries
    print("🔍 RECENT PITCH EVALUATIONS:")
    try:
        recent_evals = await conn.fetch("""
            SELECT student_id, step_name, score, evaluation_date 
            FROM pitch_evaluations 
            ORDER BY evaluation_date DESC 
            LIMIT 5;
        """)
        
        for eval_record in recent_evals:
            print(f"  - Student {eval_record['student_id']}: {eval_record['step_name']} (Score: {eval_record['score']}) at {eval_record['evaluation_date']}")
    except Exception as e:
        print(f"  Error reading pitch evaluations: {e}")
    
    print()
    
    # Check if there are any elements that should have been created
    print("🔍 CHECKING FOR MISSING ELEMENT RECORDS:")
    print("  (Elements should be created when files are uploaded)")
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(investigate_database())
