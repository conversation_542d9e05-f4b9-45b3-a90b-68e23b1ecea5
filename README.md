# The Local LLM Crash Course - Build Your Own GPT in 2 hours!
This is the courseware and Codespace for the [The Local LLM Crash Course - Build Your Own GPT in 2 hours](https://www.udemy.com/course/the-local-llm-crash-course-build-a-hugging-face-ai-chatbot/?referralCode=EAD6017AA0001257DD9A)! Have fun with the course and use the Q&A if you run into any issues!

# Course Resources

### Codespaces Pricing and Free Credits
https://docs.github.com/en/billing/managing-billing-for-github-codespaces/about-billing-for-github-codespaces

### Hugging Face
* The Orca Model's Model Card: https://huggingface.co/zoltanctoth/orca_mini_3B-GGUF

###  Installing `ctransformers` and Chainlit
Just for reference. Remember, you don't need to do this as it's pre-installed in your Codespace.
```
pip install ctransformers chainlit
```

## The Open Orca Dataset
The dataset on Hugging Face: https://huggingface.co/datasets/Open-Orca/OpenOrca

## Chainlit and Streamlit
 * Chainlit: https://docs.chainlit.io/get-started/overview
 * Streamlit: https://streamlit.io/

## LangChain

### Installation
```
pip install langchain langchain-community
```

