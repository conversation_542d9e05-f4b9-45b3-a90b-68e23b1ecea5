import os
import asyncio
import asyncpg
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def setup_feedback_table():
    """Create the feedback table in the database."""
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set.")
        return False
    
    try:
        # Connect to the database
        conn = await asyncpg.connect(database_url)
        
        # Create the feedback table if it doesn't exist
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS feedback (
                id SERIAL PRIMARY KEY,
                student_id VARCHAR(255),
                message_id VARCHAR(255),
                rating INTEGER,
                comment TEXT,
                created_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        print("Feedback table created successfully!")
        
        # Close the connection
        await conn.close()
        return True
    except Exception as e:
        print(f"Error creating feedback table: {e}")
        return False

# Run the setup function
if __name__ == "__main__":
    asyncio.run(setup_feedback_table())
